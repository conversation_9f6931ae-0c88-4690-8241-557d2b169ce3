// internal/adapters/memory/repos.go
package memory

import (
	"context"
	"errors"
	"sync"

	order "github.com/micksudev/go-qr-order/internal/core/domain"
	"github.com/micksudev/go-qr-order/internal/ports"
)

type Tables struct {
	mu     sync.RWMutex
	byCode map[string]*order.Table
}

var _ ports.TableRepo = (*Tables)(nil)

func NewTables() *Tables {
	return &Tables{byCode: make(map[string]*order.Table)}
}

func (r *Tables) FindByCode(ctx context.Context, code string) (*order.Table, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	t, ok := r.byCode[code]
	if !ok {
		return nil, errors.New("table not found")
	}
	return t, nil
}

// UpdateStatus implements ports.TableRepo.
func (r *Tables) UpdateStatus(ctx context.Context, tableID int64, status order.TableStatus) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	for _, t := range r.byCode {
		if t.ID == tableID {
			t.Status = status
			return nil
		}
	}
	return errors.New("table not found")
}

func (r *Tables) ListAll(ctx context.Context) ([]order.Table, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	tables := make([]order.Table, 0, len(r.byCode))
	for _, t := range r.byCode {
		tables = append(tables, *t)
	}

	return tables, nil
}
