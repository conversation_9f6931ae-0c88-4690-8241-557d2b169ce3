# QR Order System - Docker Commands

.PHONY: help build up down restart logs clean backup restore

# Default target
help:
	@echo "QR Order System - Docker Commands"
	@echo ""
	@echo "Available commands:"
	@echo "  build     - Build all containers"
	@echo "  up        - Start all services"
	@echo "  down      - Stop all services"
	@echo "  restart   - Restart all services"
	@echo "  logs      - View logs from all services"
	@echo "  clean     - Stop and remove all containers, networks, and volumes"
	@echo "  backup    - Backup database and uploads"
	@echo "  restore   - Restore database and uploads"
	@echo "  dev       - Start development environment"
	@echo "  prod      - Start production environment"
	@echo ""
	@echo "Service-specific commands:"
	@echo "  logs-backend   - View backend logs"
	@echo "  logs-frontend  - View frontend logs"
	@echo "  logs-db        - View database logs"
	@echo "  shell-backend  - Open shell in backend container"
	@echo "  shell-db       - Open psql in database"

# Build all containers
build:
	docker-compose build

# Start all services
up:
	docker-compose up -d

# Start with build (development)
dev:
	docker-compose up --build -d

# Start production with nginx proxy
prod:
	docker-compose --profile production up -d

# Stop all services
down:
	docker-compose down

# Restart all services
restart:
	docker-compose restart

# View logs
logs:
	docker-compose logs -f

# Service-specific logs
logs-backend:
	docker-compose logs -f backend

logs-frontend:
	docker-compose logs -f frontend

logs-db:
	docker-compose logs -f postgres

# Clean everything
clean:
	docker-compose down -v --remove-orphans
	docker system prune -f

# Shell access
shell-backend:
	docker-compose exec backend sh

shell-db:
	docker-compose exec postgres psql -U qr_user -d qr_order

# Backup
backup:
	@echo "Creating backup..."
	@mkdir -p backups
	@docker-compose exec postgres pg_dump -U qr_user qr_order > backups/db_$(shell date +%Y%m%d_%H%M%S).sql
	@docker run --rm -v qr-order_backend_uploads:/data -v $(PWD)/backups:/backup alpine tar czf /backup/uploads_$(shell date +%Y%m%d_%H%M%S).tar.gz -C /data .
	@echo "Backup completed!"

# Restore (requires backup files)
restore:
	@echo "Available backups:"
	@ls -la backups/
	@echo "To restore:"
	@echo "  Database: docker-compose exec -T postgres psql -U qr_user qr_order < backups/db_YYYYMMDD_HHMMSS.sql"
	@echo "  Uploads:  docker run --rm -v qr-order_backend_uploads:/data -v $(PWD)/backups:/backup alpine tar xzf /backup/uploads_YYYYMMDD_HHMMSS.tar.gz -C /data"

# Health check
health:
	@echo "Checking service health..."
	@docker-compose ps
	@echo ""
	@echo "Backend API:"
	@curl -s http://localhost:8080/menu > /dev/null && echo "✅ Backend OK" || echo "❌ Backend Failed"
	@echo "Frontend:"
	@curl -s http://localhost:3000 > /dev/null && echo "✅ Frontend OK" || echo "❌ Frontend Failed"
	@echo "Database:"
	@docker-compose exec postgres pg_isready -U qr_user > /dev/null && echo "✅ Database OK" || echo "❌ Database Failed"

# Update and restart
update:
	git pull
	docker-compose up --build -d

# Initialize environment
init:
	@if [ ! -f .env ]; then \
		cp .env.example .env; \
		echo "Created .env file from template"; \
		echo "Please edit .env file with your configuration"; \
	else \
		echo ".env file already exists"; \
	fi
