// internal/adapters/memory/menus.go
package memory

import (
	"context"
	"errors"
	"sync"

	order "github.com/micksudev/go-qr-order/internal/core/domain"
	"github.com/micksudev/go-qr-order/internal/ports"
)

var _ ports.MenuRepo = (*Menus)(nil)

type Menus struct {
	mu     sync.RWMutex
	byID   map[int64]*order.MenuItem
	nextID int64
}

func NewMenus() *Menus {
	return &Menus{byID: make(map[int64]*order.MenuItem), nextID: 1}
}

func (r *Menus) FindByID(ctx context.Context, id int64) (*order.MenuItem, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	m, ok := r.byID[id]
	if !ok {
		return nil, errors.New("menu not found")
	}
	return m, nil
}

func (r *Menus) ListActive(ctx context.Context) ([]order.MenuItem, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	out := make([]order.MenuItem, 0, len(r.byID))
	for _, m := range r.byID {
		if m.IsAvailable {
			out = append(out, *m)
		}
	}
	return out, nil
}

func (r *Menus) Create(ctx context.Context, item *order.MenuItem) (*order.MenuItem, error) {
	r.mu.Lock()
	defer r.mu.Unlock()

	newItem := &order.MenuItem{
		ID:          r.nextID,
		Name:        item.Name,
		Description: item.Description,
		Price:       item.Price,
		ImageURL:    item.ImageURL,
		IsAvailable: item.IsAvailable,
	}

	r.byID[r.nextID] = newItem
	r.nextID++

	return newItem, nil
}

func (r *Menus) Update(ctx context.Context, item *order.MenuItem) (*order.MenuItem, error) {
	r.mu.Lock()
	defer r.mu.Unlock()

	existing, ok := r.byID[item.ID]
	if !ok {
		return nil, errors.New("menu item not found")
	}

	existing.Name = item.Name
	existing.Description = item.Description
	existing.Price = item.Price
	existing.ImageURL = item.ImageURL
	existing.IsAvailable = item.IsAvailable

	return existing, nil
}

func (r *Menus) Delete(ctx context.Context, id int64) error {
	r.mu.Lock()
	defer r.mu.Unlock()

	if _, ok := r.byID[id]; !ok {
		return errors.New("menu item not found")
	}

	delete(r.byID, id)
	return nil
}

func (r *Menus) ListAll(ctx context.Context) ([]order.MenuItem, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	out := make([]order.MenuItem, 0, len(r.byID))
	for _, m := range r.byID {
		out = append(out, *m)
	}
	return out, nil
}
