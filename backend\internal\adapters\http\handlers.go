// internal/adapters/http/handlers.go
package httpadapter

import (
	"encoding/json"
	"net/http"

	order "github.com/micksudev/go-qr-order/internal/core/domain"
)

type addItemReq struct {
	TableCode  string `json:"table_code"`
	MenuItemID int64  `json:"menu_item_id"`
	Qty        int    `json:"qty"`
}
type orderView struct {
	ID      int64             `json:"id"`
	TableID int64             `json:"table_id"`
	Status  order.OrderStatus `json:"status"`
	Total   int64             `json:"total_satang"`
	Items   []itemView        `json:"items"`
}
type itemView struct {
	MenuItemID int64  `json:"menu_item_id"`
	Name       string `json:"name"`
	UnitPrice  int64  `json:"unit_price_satang"`
	Qty        int    `json:"qty"`
}

type menuItemPublicView struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Price       int64  `json:"price"`
	ImageURL    string `json:"image_url"`
}

func (s *Server) handleAddItem(w http.ResponseWriter, r *http.Request) {
	var req addItemReq
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "bad json", http.StatusBadRequest)
		return
	}
	o, err := s.cust.AddItem(r.Context(), req.TableCode, req.MenuItemID, req.Qty)
	if err != nil {
		writeErr(w, err)
		return
	}
	writeJSON(w, toOrderView(o))
}

func (s *Server) handleSubmit(w http.ResponseWriter, r *http.Request) {
	var req struct {
		TableCode string `json:"table_code"`
	}
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "bad json", http.StatusBadRequest)
		return
	}
	o, err := s.cust.SubmitOrder(r.Context(), req.TableCode)
	if err != nil {
		writeErr(w, err)
		return
	}
	writeJSON(w, toOrderView(o))
}

func toOrderView(o *order.Order) orderView {
	items := make([]itemView, 0, len(o.Items))
	for _, it := range o.Items {
		items = append(items, itemView{
			MenuItemID: it.MenuItemID, Name: it.Name, UnitPrice: it.UnitPrice, Qty: it.Qty,
		})
	}
	return orderView{
		ID: o.ID, TableID: o.TableID, Status: o.Status, Total: o.Total, Items: items,
	}
}

func writeJSON(w http.ResponseWriter, v any) {
	w.Header().Set("Content-Type", "application/json")
	_ = json.NewEncoder(w).Encode(v)
}

func (s *Server) handleGetMenu(w http.ResponseWriter, r *http.Request) {
	items, err := s.adminMenu.ListAllMenuItems(r.Context())
	if err != nil {
		writeErr(w, err)
		return
	}

	// Filter only available items for public view
	var availableItems []menuItemPublicView
	for _, item := range items {
		if item.IsAvailable {
			availableItems = append(availableItems, menuItemPublicView{
				ID:          item.ID,
				Name:        item.Name,
				Description: item.Description,
				Price:       item.Price,
				ImageURL:    item.ImageURL,
			})
		}
	}

	writeJSON(w, availableItems)
}

func writeErr(w http.ResponseWriter, err error) {
	// ชั่วคราว: map error ง่ายๆ (ภายหลังค่อยปรับให้สวย)
	http.Error(w, err.Error(), http.StatusBadRequest)
}
