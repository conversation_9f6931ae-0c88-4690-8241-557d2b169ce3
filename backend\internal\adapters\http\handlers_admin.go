package httpadapter

import (
	"context"
	"encoding/json"
	"fmt"
	"io"
	"net/http"
	"os"
	"path/filepath"
	"strconv"
	"strings"
	"time"

	"github.com/go-chi/chi/v5"
	order "github.com/micksudev/go-qr-order/internal/core/domain"
)

// สมมุติ Server มี field: kitchen ports.KitchenCommandPort ด้วย
func (s *Server) adminRoutes(r chi.Router) {
	// Table management routes
	r.Get("/tables", s.handleListTables)

	// Order management routes
	r.Get("/orders", s.handleListOrders)
	r.Get("/orders/{id}", s.handleGetOrder)
	r.Post("/orders/{id}/accept", s.handleAccept)
	r.Post("/orders/{id}/preparing", s.handlePreparing)
	r.Post("/orders/{id}/ready", s.handleReady)
	r.Post("/orders/{id}/serve", s.handleServe)
	r.Post("/orders/{id}/cancel", s.handleCancel)

	// Menu management routes
	r.Route("/menu", func(r chi.Router) {
		r.Get("/", s.handleListMenuItems)
		r.Post("/", s.handleCreateMenuItem)
		r.Get("/{id}", s.handleGetMenuItem)
		r.Put("/{id}", s.handleUpdateMenuItem)
		r.Delete("/{id}", s.handleDeleteMenuItem)
		r.Post("/{id}/upload-image", s.handleUploadMenuImage)
	})
}

func (s *Server) handleAccept(w http.ResponseWriter, r *http.Request) {
	s.updateStatus(w, r, s.kitchen.AcceptOrder)
}
func (s *Server) handlePreparing(w http.ResponseWriter, r *http.Request) {
	s.updateStatus(w, r, s.kitchen.MarkPreparing)
}
func (s *Server) handleReady(w http.ResponseWriter, r *http.Request) {
	s.updateStatus(w, r, s.kitchen.MarkReady)
}
func (s *Server) handleServe(w http.ResponseWriter, r *http.Request) {
	s.updateStatus(w, r, s.kitchen.Serve)
}
func (s *Server) handleCancel(w http.ResponseWriter, r *http.Request) {
	s.updateStatus(w, r, s.kitchen.Cancel)
}

func (s *Server) updateStatus(w http.ResponseWriter, r *http.Request, fn func(ctx context.Context, id int64) error) {
	idStr := chi.URLParam(r, "id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		http.Error(w, "bad id", http.StatusBadRequest)
		return
	}
	if err := fn(r.Context(), id); err != nil {
		writeErr(w, err)
		return
	}
	w.WriteHeader(http.StatusNoContent)
}

// Menu management handlers
type createMenuItemReq struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Price       int64  `json:"price"`
	ImageURL    string `json:"image_url"`
}

type updateMenuItemReq struct {
	Name        string `json:"name"`
	Description string `json:"description"`
	Price       int64  `json:"price"`
	ImageURL    string `json:"image_url"`
	IsAvailable bool   `json:"is_available"`
}

type menuItemView struct {
	ID          int64  `json:"id"`
	Name        string `json:"name"`
	Description string `json:"description"`
	Price       int64  `json:"price"`
	ImageURL    string `json:"image_url"`
	IsAvailable bool   `json:"is_available"`
}

func toMenuItemView(item *order.MenuItem) menuItemView {
	return menuItemView{
		ID:          item.ID,
		Name:        item.Name,
		Description: item.Description,
		Price:       item.Price,
		ImageURL:    item.ImageURL,
		IsAvailable: item.IsAvailable,
	}
}

func (s *Server) handleListMenuItems(w http.ResponseWriter, r *http.Request) {
	items, err := s.adminMenu.ListAllMenuItems(r.Context())
	if err != nil {
		writeErr(w, err)
		return
	}

	views := make([]menuItemView, len(items))
	for i, item := range items {
		views[i] = toMenuItemView(&item)
	}

	writeJSON(w, views)
}

func (s *Server) handleCreateMenuItem(w http.ResponseWriter, r *http.Request) {
	var req createMenuItemReq
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "bad json", http.StatusBadRequest)
		return
	}

	item, err := s.adminMenu.CreateMenuItem(r.Context(), req.Name, req.Description, req.ImageURL, req.Price)
	if err != nil {
		writeErr(w, err)
		return
	}

	writeJSON(w, toMenuItemView(item))
}

func (s *Server) handleGetMenuItem(w http.ResponseWriter, r *http.Request) {
	idStr := chi.URLParam(r, "id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		http.Error(w, "bad id", http.StatusBadRequest)
		return
	}

	item, err := s.adminMenu.GetMenuItem(r.Context(), id)
	if err != nil {
		writeErr(w, err)
		return
	}

	writeJSON(w, toMenuItemView(item))
}

func (s *Server) handleUpdateMenuItem(w http.ResponseWriter, r *http.Request) {
	idStr := chi.URLParam(r, "id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		http.Error(w, "bad id", http.StatusBadRequest)
		return
	}

	var req updateMenuItemReq
	if err := json.NewDecoder(r.Body).Decode(&req); err != nil {
		http.Error(w, "bad json", http.StatusBadRequest)
		return
	}

	item, err := s.adminMenu.UpdateMenuItem(r.Context(), id, req.Name, req.Description, req.ImageURL, req.Price, req.IsAvailable)
	if err != nil {
		writeErr(w, err)
		return
	}

	writeJSON(w, toMenuItemView(item))
}

func (s *Server) handleDeleteMenuItem(w http.ResponseWriter, r *http.Request) {
	idStr := chi.URLParam(r, "id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		http.Error(w, "bad id", http.StatusBadRequest)
		return
	}

	if err := s.adminMenu.DeleteMenuItem(r.Context(), id); err != nil {
		writeErr(w, err)
		return
	}

	w.WriteHeader(http.StatusNoContent)
}

func (s *Server) handleUploadMenuImage(w http.ResponseWriter, r *http.Request) {
	idStr := chi.URLParam(r, "id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		http.Error(w, "bad id", http.StatusBadRequest)
		return
	}

	// Parse multipart form
	err = r.ParseMultipartForm(10 << 20) // 10 MB max
	if err != nil {
		http.Error(w, "failed to parse form", http.StatusBadRequest)
		return
	}

	file, header, err := r.FormFile("image")
	if err != nil {
		http.Error(w, "no image file provided", http.StatusBadRequest)
		return
	}
	defer file.Close()

	// Validate file type
	contentType := header.Header.Get("Content-Type")
	if !strings.HasPrefix(contentType, "image/") {
		http.Error(w, "file must be an image", http.StatusBadRequest)
		return
	}

	// Create uploads directory if it doesn't exist
	uploadsDir := "uploads/menu"
	if err := os.MkdirAll(uploadsDir, 0755); err != nil {
		http.Error(w, "failed to create upload directory", http.StatusInternalServerError)
		return
	}

	// Generate unique filename
	ext := filepath.Ext(header.Filename)
	filename := fmt.Sprintf("%d_%d%s", id, time.Now().Unix(), ext)
	filePath := filepath.Join(uploadsDir, filename)

	// Save file
	dst, err := os.Create(filePath)
	if err != nil {
		http.Error(w, "failed to create file", http.StatusInternalServerError)
		return
	}
	defer dst.Close()

	if _, err := io.Copy(dst, file); err != nil {
		http.Error(w, "failed to save file", http.StatusInternalServerError)
		return
	}

	// Update menu item with new image URL
	imageURL := "/" + strings.ReplaceAll(filePath, "\\", "/")

	// Get current menu item
	item, err := s.adminMenu.GetMenuItem(r.Context(), id)
	if err != nil {
		http.Error(w, "menu item not found", http.StatusNotFound)
		return
	}

	// Update with new image URL
	updatedItem, err := s.adminMenu.UpdateMenuItem(r.Context(), id, item.Name, item.Description, imageURL, item.Price, item.IsAvailable)
	if err != nil {
		writeErr(w, err)
		return
	}

	writeJSON(w, map[string]interface{}{
		"message":   "Image uploaded successfully",
		"image_url": imageURL,
		"item":      toMenuItemView(updatedItem),
	})
}

// Order management handlers - using existing types from handlers.go
func toAdminOrderView(o *order.Order) orderView {
	items := make([]itemView, 0, len(o.Items))
	for _, it := range o.Items {
		items = append(items, itemView{
			MenuItemID: it.MenuItemID,
			Name:       it.Name,
			UnitPrice:  it.UnitPrice,
			Qty:        it.Qty,
		})
	}
	return orderView{
		ID:      o.ID,
		TableID: o.TableID,
		Status:  o.Status,
		Total:   o.Total,
		Items:   items,
	}
}

func (s *Server) handleListOrders(w http.ResponseWriter, r *http.Request) {
	status := r.URL.Query().Get("status")

	var orders []order.Order
	var err error

	if status != "" {
		orders, err = s.adminOrder.ListOrdersByStatus(r.Context(), order.OrderStatus(status))
	} else {
		orders, err = s.adminOrder.ListAllOrders(r.Context())
	}

	if err != nil {
		writeErr(w, err)
		return
	}

	views := make([]orderView, len(orders))
	for i, o := range orders {
		views[i] = toAdminOrderView(&o)
	}

	writeJSON(w, views)
}

func (s *Server) handleGetOrder(w http.ResponseWriter, r *http.Request) {
	idStr := chi.URLParam(r, "id")
	id, err := strconv.ParseInt(idStr, 10, 64)
	if err != nil {
		http.Error(w, "bad id", http.StatusBadRequest)
		return
	}

	order, err := s.adminOrder.GetOrder(r.Context(), id)
	if err != nil {
		writeErr(w, err)
		return
	}

	writeJSON(w, toAdminOrderView(order))
}

// Table management handlers
type tableView struct {
	ID     int64  `json:"id"`
	Code   string `json:"code"`
	Status string `json:"status"`
}

func toTableView(table *order.Table) tableView {
	return tableView{
		ID:     table.ID,
		Code:   table.Code,
		Status: string(table.Status),
	}
}

func (s *Server) handleListTables(w http.ResponseWriter, r *http.Request) {
	tables, err := s.adminOrder.ListAllTables(r.Context())
	if err != nil {
		writeErr(w, err)
		return
	}

	views := make([]tableView, len(tables))
	for i, t := range tables {
		views[i] = toTableView(&t)
	}

	writeJSON(w, views)
}
