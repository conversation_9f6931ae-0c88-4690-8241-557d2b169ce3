// internal/adapters/http/server.go
package httpadapter

import (
	"net/http"
	"os"
	"strings"

	"github.com/go-chi/chi/v5"
	"github.com/go-chi/chi/v5/middleware"
	"github.com/go-chi/cors"
	"github.com/micksudev/go-qr-order/internal/ports"
)

type Server struct {
	cust       ports.CustomerOrderingPort
	kitchen    ports.KitchenCommandPort
	adminMenu  ports.AdminMenuPort
	adminOrder ports.AdminOrderPort
}

func NewServer(cust ports.CustomerOrderingPort, kitchen ports.KitchenCommandPort, adminMenu ports.AdminMenuPort, adminOrder ports.AdminOrderPort) *Server {
	return &Server{cust: cust, kitchen: kitchen, adminMenu: adminMenu, adminOrder: adminOrder}
}

func (s *Server) Routes() http.Handler {
	r := chi.NewRouter()

	// Get CORS origins from environment variable
	corsOrigins := os.Getenv("CORS_ORIGINS")
	if corsOrigins == "" {
		corsOrigins = "http://localhost:3000,http://localhost:5173"
	}
	allowedOrigins := strings.Split(corsOrigins, ",")

	r.Use(cors.Handler(cors.Options{
		AllowedOrigins:   allowedOrigins,
		AllowedMethods:   []string{"GET", "POST", "PUT", "DELETE", "OPTIONS"},
		AllowedHeaders:   []string{"Accept", "Authorization", "Content-Type", "X-CSRF-Token"},
		AllowCredentials: true,
		MaxAge:           300,
	}))

	r.Use(middleware.RequestID, middleware.RealIP, middleware.Recoverer, middleware.Logger)

	// Serve static files for uploaded images
	r.Handle("/uploads/*", http.StripPrefix("/uploads/", http.FileServer(http.Dir("uploads/"))))

	r.Get("/menu", s.handleGetMenu)
	r.Post("/cart/items", s.handleAddItem)
	r.Post("/orders/submit", s.handleSubmit)

	r.Route("/admin", s.adminRoutes)
	return r
}
