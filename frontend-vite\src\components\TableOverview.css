.table-overview {
  max-width: 1200px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
}

.header {
  text-align: center;
  margin-bottom: 40px;
}

.header h1 {
  color: #2c3e50;
  font-size: 2.5rem;
  margin-bottom: 10px;
}

.header p {
  color: #7f8c8d;
  font-size: 1.2rem;
}

.tables-grid {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
  gap: 30px;
  margin-bottom: 40px;
}

.table-card {
  background: white;
  border-radius: 15px;
  padding: 25px;
  text-align: center;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
  border: 2px solid #ecf0f1;
}

.table-card:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.table-card h3 {
  color: #2c3e50;
  font-size: 1.5rem;
  margin-bottom: 20px;
}

.qr-code-container {
  margin: 20px 0;
  display: flex;
  justify-content: center;
}

.qr-code-container img {
  border-radius: 10px;
  border: 3px solid #ecf0f1;
}

.table-id {
  color: #95a5a6;
  font-weight: bold;
  font-size: 1.1rem;
  margin: 15px 0;
}

.test-link {
  display: inline-block;
  background: #3498db;
  color: white;
  padding: 10px 20px;
  text-decoration: none;
  border-radius: 25px;
  font-weight: bold;
  transition: background 0.3s ease;
}

.test-link:hover {
  background: #2980b9;
}

.admin-section {
  text-align: center;
  padding-top: 30px;
  border-top: 2px solid #ecf0f1;
}

.admin-link {
  display: inline-block;
  background: #e74c3c;
  color: white;
  padding: 15px 30px;
  text-decoration: none;
  border-radius: 30px;
  font-weight: bold;
  font-size: 1.1rem;
  transition: background 0.3s ease;
}

.admin-link:hover {
  background: #c0392b;
}

/* Responsive Design */
@media (max-width: 768px) {
  .table-overview {
    padding: 15px;
  }
  
  .header h1 {
    font-size: 2rem;
  }
  
  .header p {
    font-size: 1rem;
  }
  
  .tables-grid {
    grid-template-columns: 1fr;
    gap: 20px;
  }
  
  .table-card {
    padding: 20px;
  }
}
