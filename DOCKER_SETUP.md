# QR Order System - Docker Setup

## 🐳 Docker Compose Configuration

ระบบนี้ประกอบด้วย 4 services หลัก:

### Services:
1. **PostgreSQL Database** - ฐานข้อมูลหลัก
2. **Backend API** - Go API server
3. **Frontend** - React app (Nginx)
4. **Nginx Proxy** - Reverse proxy (optional)

## 🚀 Quick Start

### 1. Clone และเตรียม Environment
```bash
# Copy environment file
cp .env.example .env

# Edit environment variables if needed
nano .env
```

### 2. Build และ Run ทั้งระบบ
```bash
# Build และ start ทุก services
docker-compose up --build

# หรือ run ใน background
docker-compose up --build -d
```

### 3. เข้าใช้งานระบบ
- **Frontend:** http://localhost:3000
- **Backend API:** http://localhost:5050
- **Database:** localhost:5432
- **Nginx Proxy:** http://localhost (ถ้าเปิดใช้)

## 📋 Available Commands

### Development
```bash
# Start services
docker-compose up

# Start in background
docker-compose up -d

# Build และ start
docker-compose up --build

# Stop services
docker-compose down

# Stop และลบ volumes
docker-compose down -v

# View logs
docker-compose logs
docker-compose logs backend
docker-compose logs frontend
```

### Production
```bash
# Start with nginx proxy
docker-compose --profile production up -d

# Scale backend instances
docker-compose up --scale backend=3 -d
```

## 🔧 Service Details

### PostgreSQL Database
- **Port:** 5432
- **Database:** qr_order
- **User:** qr_user
- **Password:** qr_password
- **Volume:** postgres_data

### Backend API
- **Port:** 5050
- **Health Check:** GET /menu
- **Uploads Volume:** backend_uploads
- **Auto-restart:** unless-stopped

### Frontend
- **Port:** 3000 (mapped to 80 inside container)
- **Built with:** Nginx Alpine
- **SPA Routing:** Configured

### Nginx Proxy (Optional)
- **Port:** 80, 443
- **Routes:**
  - `/` → Frontend
  - `/api/*` → Backend API
  - `/ws` → WebSocket
  - `/uploads/*` → Static files

## 🗂️ File Structure
```
.
├── docker-compose.yml          # Main compose file
├── init.sql                   # Database initialization
├── .env.example              # Environment template
├── backend/
│   ├── Dockerfile            # Backend container
│   └── .dockerignore
├── frontend-vite/
│   ├── Dockerfile            # Frontend container
│   ├── nginx.conf            # Frontend nginx config
│   └── .dockerignore
└── nginx/
    ├── nginx.conf            # Main nginx config
    └── conf.d/
        └── default.conf      # Proxy configuration
```

## 🔍 Troubleshooting

### Database Connection Issues
```bash
# Check database status
docker-compose ps postgres

# View database logs
docker-compose logs postgres

# Connect to database
docker-compose exec postgres psql -U qr_user -d qr_order
```

### Backend Issues
```bash
# Check backend logs
docker-compose logs backend

# Restart backend only
docker-compose restart backend

# Rebuild backend
docker-compose up --build backend
```

### Frontend Issues
```bash
# Check frontend logs
docker-compose logs frontend

# Rebuild frontend
docker-compose up --build frontend
```

### Network Issues
```bash
# Check network
docker network ls
docker network inspect qr-order_qr-order-network

# Test connectivity
docker-compose exec backend ping postgres
docker-compose exec frontend ping backend
```

## 🔒 Security Notes

### Development
- Default passwords ใน .env.example
- CORS เปิดสำหรับ localhost
- Debug logging enabled

### Production
- เปลี่ยน passwords ใน .env
- ตั้งค่า CORS ให้เฉพาะเจาะจง
- ใช้ HTTPS certificates
- ปิด debug logging

## 📊 Monitoring

### Health Checks
```bash
# Check all services health
docker-compose ps

# Backend health
curl http://localhost:5050/menu

# Frontend health
curl http://localhost:3000

# Database health
docker-compose exec postgres pg_isready -U qr_user
```

### Logs
```bash
# Follow all logs
docker-compose logs -f

# Follow specific service
docker-compose logs -f backend

# Last 100 lines
docker-compose logs --tail=100
```

## 🔄 Updates

### Update Code
```bash
# Pull latest code
git pull

# Rebuild and restart
docker-compose up --build -d
```

### Update Dependencies
```bash
# Rebuild without cache
docker-compose build --no-cache

# Start with new images
docker-compose up -d
```

## 💾 Backup & Restore

### Database Backup
```bash
# Create backup
docker-compose exec postgres pg_dump -U qr_user qr_order > backup.sql

# Restore backup
docker-compose exec -T postgres psql -U qr_user qr_order < backup.sql
```

### Volume Backup
```bash
# Backup uploads
docker run --rm -v qr-order_backend_uploads:/data -v $(pwd):/backup alpine tar czf /backup/uploads.tar.gz -C /data .

# Restore uploads
docker run --rm -v qr-order_backend_uploads:/data -v $(pwd):/backup alpine tar xzf /backup/uploads.tar.gz -C /data
```

## 🎯 Next Steps

1. ตั้งค่า SSL certificates สำหรับ production
2. เพิ่ม monitoring (Prometheus/Grafana)
3. ตั้งค่า CI/CD pipeline
4. เพิ่ม backup automation
5. ตั้งค่า load balancing
