# Production configuration
# Usage: docker-compose -f docker-compose.yml -f docker-compose.prod.yml up -d

services:
  # Production backend
  backend:
    restart: always
    environment:
      - GO_ENV=production
      - DEBUG=false
    deploy:
      replicas: 2
      resources:
        limits:
          cpus: '1.0'
          memory: 512M
        reservations:
          cpus: '0.5'
          memory: 256M

  # Production frontend
  frontend:
    restart: always
    environment:
      - NODE_ENV=production
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M

  # Production database
  postgres:
    restart: always
    environment:
      - POSTGRES_DB=qr_order_prod
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    # Don't expose port in production
    ports: []

  # Production nginx proxy
  nginx:
    profiles:
      - production
    restart: always
    deploy:
      resources:
        limits:
          cpus: '0.5'
          memory: 256M
        reservations:
          cpus: '0.25'
          memory: 128M
