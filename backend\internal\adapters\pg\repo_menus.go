// internal/adapters/pg/repo_menus.go
package pg

import (
	"context"
	"errors"

	dom "github.com/micksudev/go-qr-order/internal/core/domain"
	"github.com/micksudev/go-qr-order/internal/ports"
	"gorm.io/gorm"
)

var _ ports.MenuRepo = (*Menus)(nil)

type Menus struct{ db *gorm.DB }

// ListActive implements ports.MenuRepo.
func (r *Menus) ListActive(ctx context.Context) ([]dom.MenuItem, error) {
	var models []MenuItemModel
	if err := r.db.WithContext(ctx).Where("is_available = ?", true).Find(&models).Error; err != nil {
		return nil, err
	}

	items := make([]dom.MenuItem, len(models))
	for i, m := range models {
		items[i] = dom.MenuItem{
			ID: m.ID, Name: m.Name, Description: m.Description,
			Price: m.Price, ImageURL: m.ImageURL, IsAvailable: m.Is<PERSON>vailable,
		}
	}
	return items, nil
}

func (r *Menus) Create(ctx context.Context, item *dom.MenuItem) (*dom.MenuItem, error) {
	model := MenuItemModel{
		Name:        item.Name,
		Description: item.Description,
		Price:       item.Price,
		ImageURL:    item.ImageURL,
		IsAvailable: item.IsAvailable,
	}

	if err := r.db.WithContext(ctx).Create(&model).Error; err != nil {
		return nil, err
	}

	return &dom.MenuItem{
		ID: model.ID, Name: model.Name, Description: model.Description,
		Price: model.Price, ImageURL: model.ImageURL, IsAvailable: model.IsAvailable,
	}, nil
}

func (r *Menus) Update(ctx context.Context, item *dom.MenuItem) (*dom.MenuItem, error) {
	model := MenuItemModel{
		ID:          item.ID,
		Name:        item.Name,
		Description: item.Description,
		Price:       item.Price,
		ImageURL:    item.ImageURL,
		IsAvailable: item.IsAvailable,
	}

	if err := r.db.WithContext(ctx).Save(&model).Error; err != nil {
		return nil, err
	}

	return item, nil
}

func (r *Menus) Delete(ctx context.Context, id int64) error {
	return r.db.WithContext(ctx).Delete(&MenuItemModel{}, id).Error
}

func (r *Menus) ListAll(ctx context.Context) ([]dom.MenuItem, error) {
	var models []MenuItemModel
	if err := r.db.WithContext(ctx).Find(&models).Error; err != nil {
		return nil, err
	}

	items := make([]dom.MenuItem, len(models))
	for i, m := range models {
		items[i] = dom.MenuItem{
			ID: m.ID, Name: m.Name, Description: m.Description,
			Price: m.Price, ImageURL: m.ImageURL, IsAvailable: m.IsAvailable,
		}
	}
	return items, nil
}

func NewMenus(pg *PG) *Menus        { return &Menus{db: pg.DB} }
func NewMenusTX(tx *gorm.DB) *Menus { return &Menus{db: tx} }

func (r *Menus) FindByID(ctx context.Context, id int64) (*dom.MenuItem, error) {
	var m MenuItemModel
	if err := r.db.WithContext(ctx).First(&m, id).Error; err != nil {
		return nil, errors.New("menu not found")
	}
	return &dom.MenuItem{
		ID: m.ID, Name: m.Name, Description: m.Description,
		Price: m.Price, ImageURL: m.ImageURL, IsAvailable: m.IsAvailable,
	}, nil
}
