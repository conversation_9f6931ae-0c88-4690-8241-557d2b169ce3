import React, { useState, useEffect } from 'react'
import { Link } from 'react-router-dom'
import './AdminPage.css'
import { AdminWebSocketManager, adminOrderAPI, utils } from '../services/api'
import type { Order as APIOrder, Table as APITable } from '../services/api'

const AdminPage: React.FC = () => {
  const [orders, setOrders] = useState<APIOrder[]>([])
  const [tables, setTables] = useState<APITable[]>([])
  const [wsConnected, setWsConnected] = useState(false)
  const [backendConnected, setBackendConnected] = useState(false)
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadData()
    setupWebSocket()
  }, [])

  const loadData = async () => {
    try {
      setLoading(true)

      // Load orders and tables from API
      const [ordersData, tablesData] = await Promise.all([
        adminOrderAPI.getAllOrders(),
        adminOrderAPI.getAllTables()
      ])

      setOrders(ordersData)
      setTables(tablesData)
      setBackendConnected(true)
    } catch (err) {
      setBackendConnected(false)
      console.error('Backend connection failed:', err)
    } finally {
      setLoading(false)
    }
  }

  const setupWebSocket = () => {
    const wsManager = new AdminWebSocketManager((connected: boolean) => {
      setWsConnected(connected)
    })

    // Subscribe to new orders
    wsManager.subscribe('order.submitted', (data: any) => {
      console.log('New order submitted:', data)

      // Convert WebSocket items to frontend format
      const convertedItems = (data.items || []).map((item: any) => ({
        menu_item_id: item.MenuItemID,
        name: item.Name,
        qty: item.Qty,
        unit_price_satang: item.UnitPrice
      }))

      // Create order object from WebSocket data
      const newOrder: APIOrder = {
        id: data.order_id,
        table_id: data.table_id,
        status: data.status,
        total_satang: data.total_satang, // Use correct field name
        items: convertedItems
      }

      // Add new order to the list
      setOrders(prevOrders => {
        // Check if order already exists (avoid duplicates)
        const exists = prevOrders.some(order => order.id === newOrder.id)
        if (exists) {
          return prevOrders
        }
        return [...prevOrders, newOrder]
      })
    })

    // Subscribe to order status changes
    wsManager.subscribe('order.status.changed', (data: any) => {
      console.log('Order status changed:', data)

      setOrders(prevOrders =>
        prevOrders.map(order =>
          order.id === data.order_id ? { ...order, status: data.status } : order
        )
      )
    })

    // Cleanup on unmount
    return () => {
      wsManager.disconnect()
    }
  }

  const updateOrderStatus = async (orderId: number, newStatus: string) => {
    try {
      // Call appropriate API based on status
      switch (newStatus) {
        case 'ACCEPTED':
          await adminOrderAPI.acceptOrder(orderId)
          break
        case 'PREPARING':
          await adminOrderAPI.markPreparing(orderId)
          break
        case 'READY':
          await adminOrderAPI.markReady(orderId)
          break
        case 'SERVED':
          await adminOrderAPI.serve(orderId)
          break
        case 'CANCELED':
          await adminOrderAPI.cancelOrder(orderId)
          break
      }

      // Reload data to get updated status
      await loadData()
    } catch (err) {
      console.error('Error updating order status:', err)
      alert('เกิดข้อผิดพลาดในการอัพเดตสถานะ')
    }
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'DRAFT': return '#95a5a6'
      case 'SUBMITTED': return '#e74c3c'
      case 'ACCEPTED': return '#f39c12'
      case 'PREPARING': return '#3498db'
      case 'READY': return '#27ae60'
      case 'SERVED': return '#2ecc71'
      case 'CANCELED': return '#e74c3c'
      default: return '#34495e'
    }
  }

  const getStatusText = (status: string) => {
    switch (status) {
      case 'DRAFT': return 'ร่าง'
      case 'SUBMITTED': return 'ส่งแล้ว'
      case 'ACCEPTED': return 'รับออเดอร์'
      case 'PREPARING': return 'กำลังเตรียม'
      case 'READY': return 'พร้อมเสิร์ฟ'
      case 'SERVED': return 'เสิร์ฟแล้ว'
      case 'CANCELED': return 'ยกเลิก'
      default: return status
    }
  }

  const getTableCode = (tableId: number) => {
    const table = tables.find(t => t.id === tableId)
    return table ? table.code : `Table ${tableId}`
  }

  if (loading) {
    return (
      <div className="admin-page">
        <div className="admin-header">
          <h1>หน้าหลังร้าน - ระบบจัดการออเดอร์</h1>
        </div>
        <div className="loading">กำลังโหลดข้อมูล...</div>
      </div>
    )
  }

  // Don't show error page if it's just backend connection issue
  // Show normal page with connection status instead

  return (
    <div className="admin-page">
      <div className="admin-header">
        <div className="header-left">
          <h1>หน้าหลังร้าน - ระบบจัดการออเดอร์</h1>
          <div className="admin-nav">
            <Link to="/admin/menu" className="nav-link">
              📋 จัดการเมนู
            </Link>
          </div>
        </div>
        <div className="connection-status">
          <span className={`status-indicator ${backendConnected && wsConnected ? 'connected' : 'disconnected'}`}>
            {backendConnected && wsConnected ? '🟢 เชื่อมต่อแล้ว' : '🔴 ไม่ได้เชื่อมต่อ'}
          </span>
        </div>
      </div>

      {!backendConnected && !loading && (
        <div className="connection-warning">
          <p>🔴 ไม่สามารถเชื่อมต่อกับเซิร์ฟเวอร์ได้</p>
          <button onClick={loadData} className="retry-btn">ลองเชื่อมต่อใหม่</button>
        </div>
      )}

      <div className="stats-bar">
        <div className="stat-item">
          <span className="stat-number">{orders.filter(o => o.status === 'SUBMITTED').length}</span>
          <span className="stat-label">ส่งแล้ว</span>
        </div>
        <div className="stat-item">
          <span className="stat-number">{orders.filter(o => o.status === 'PREPARING').length}</span>
          <span className="stat-label">กำลังเตรียม</span>
        </div>
        <div className="stat-item">
          <span className="stat-number">{orders.filter(o => o.status === 'READY').length}</span>
          <span className="stat-label">พร้อมเสิร์ฟ</span>
        </div>
        <div className="stat-item">
          <span className="stat-number">{orders.length}</span>
          <span className="stat-label">ทั้งหมด</span>
        </div>
      </div>

      <div className="orders-container">
        {orders.length === 0 ? (
          <div className="no-orders">
            <p>ไม่มีออเดอร์ในขณะนี้</p>
          </div>
        ) : (
          <div className="orders-grid">
            {orders
              .filter(order => order.status !== 'SERVED' && order.status !== 'CANCELED' && order.status !== 'DRAFT')
              .sort((a, b) => a.id - b.id)
              .map(order => (
                <div key={order.id} className="order-card">
                  <div className="order-header">
                    <div className="table-info">
                      <h3>โต๊ะ {getTableCode(order.table_id)}</h3>
                      <span className="order-id">
                        ออเดอร์ #{order.id}
                      </span>
                    </div>
                    <div
                      className="status-badge"
                      style={{ backgroundColor: getStatusColor(order.status) }}
                    >
                      {getStatusText(order.status)}
                    </div>
                  </div>

                  <div className="order-items">
                    {order.items.map((item, index) => (
                      <div key={index} className="order-item">
                        <span className="item-name">{item.name}</span>
                        <span className="item-quantity">x{item.qty}</span>
                        <span className="item-price">{utils.formatPrice(item.unit_price_satang * item.qty)}</span>
                      </div>
                    ))}
                  </div>

                  <div className="order-footer">
                    <div className="total-price">
                      รวม: {utils.formatPrice(order.total_satang)}
                    </div>
                    <div className="action-buttons">
                      {order.status === 'SUBMITTED' && (
                        <button
                          className="btn btn-preparing"
                          onClick={() => updateOrderStatus(order.id, 'ACCEPTED')}
                        >
                          รับออเดอร์
                        </button>
                      )}
                      {order.status === 'ACCEPTED' && (
                        <button
                          className="btn btn-preparing"
                          onClick={() => updateOrderStatus(order.id, 'PREPARING')}
                        >
                          เริ่มเตรียม
                        </button>
                      )}
                      {order.status === 'PREPARING' && (
                        <button
                          className="btn btn-ready"
                          onClick={() => updateOrderStatus(order.id, 'READY')}
                        >
                          พร้อมเสิร์ฟ
                        </button>
                      )}
                      {order.status === 'READY' && (
                        <button
                          className="btn btn-complete"
                          onClick={() => updateOrderStatus(order.id, 'SERVED')}
                        >
                          เสิร์ฟแล้ว
                        </button>
                      )}
                      {(order.status === 'SUBMITTED' || order.status === 'ACCEPTED') && (
                        <button
                          className="btn btn-cancel"
                          onClick={() => updateOrderStatus(order.id, 'CANCELED')}
                        >
                          ยกเลิก
                        </button>
                      )}
                    </div>
                  </div>
                </div>
              ))}
          </div>
        )}
      </div>

      <div className="back-to-home">
        <a href="/" className="home-link">
          กลับหน้าหลัก
        </a>
      </div>
    </div>
  )
}

export default AdminPage
