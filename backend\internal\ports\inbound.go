package ports

import (
	"context"

	order "github.com/micksudev/go-qr-order/internal/core/domain"
)

type CustomerOrderingPort interface {
	OpenTable(ctx context.Context, tableCode string) (*order.Table, error)
	AddItem(ctx context.Context, tableCode string, menuItemID int64, qty int) (*order.Order, error)
	SubmitOrder(ctx context.Context, tableCode string) (*order.Order, error)
}

type KitchenCommandPort interface {
	AcceptOrder(ctx context.Context, orderID int64) error
	MarkPreparing(ctx context.Context, orderID int64) error
	MarkReady(ctx context.Context, orderID int64) error
	Serve(ctx context.Context, orderID int64) error
	Cancel(ctx context.Context, orderID int64) error
}

type AdminMenuPort interface {
	CreateMenuItem(ctx context.Context, name, description, imageURL string, price int64) (*order.MenuItem, error)
	UpdateMenuItem(ctx context.Context, id int64, name, description, imageURL string, price int64, isAvailable bool) (*order.MenuItem, error)
	DeleteMenuItem(ctx context.Context, id int64) error
	GetMenuItem(ctx context.Context, id int64) (*order.MenuItem, error)
	ListAllMenuItems(ctx context.Context) ([]order.MenuItem, error)
}

type AdminOrderPort interface {
	ListAllOrders(ctx context.Context) ([]order.Order, error)
	ListOrdersByStatus(ctx context.Context, status order.OrderStatus) ([]order.Order, error)
	GetOrder(ctx context.Context, id int64) (*order.Order, error)
	ListAllTables(ctx context.Context) ([]order.Table, error)
}
