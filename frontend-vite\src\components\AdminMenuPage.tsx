import React, { useState, useEffect } from 'react'
import { adminMenuAPI, utils } from '../services/api'
import type { AdminMenuItem, UpdateMenuItemRequest } from '../services/api'
import './AdminMenuPage.css'

interface MenuFormData {
  name: string
  description: string
  price: number
  image_url: string
  is_available: boolean
}

const AdminMenuPage: React.FC = () => {
  const [menuItems, setMenuItems] = useState<AdminMenuItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [showForm, setShowForm] = useState(false)
  const [editingItem, setEditingItem] = useState<AdminMenuItem | null>(null)
  const [formData, setFormData] = useState<MenuFormData>({
    name: '',
    description: '',
    price: 0,
    image_url: '',
    is_available: true
  })
  const [imageFile, setImageFile] = useState<File | null>(null)
  const [uploading, setUploading] = useState(false)

  useEffect(() => {
    loadMenuItems()
  }, [])

  const loadMenuItems = async () => {
    try {
      setLoading(true)
      setError(null)
      const items = await adminMenuAPI.getAllItems()
      setMenuItems(items)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load menu items')
      console.error('Error loading menu items:', err)
    } finally {
      setLoading(false)
    }
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!formData.name.trim()) {
      alert('กรุณาใส่ชื่อเมนู')
      return
    }

    if (formData.price <= 0) {
      alert('กรุณาใส่ราคาที่ถูกต้อง')
      return
    }

    try {
      setLoading(true)
      
      const requestData = {
        name: formData.name,
        description: formData.description,
        price: utils.bahtToSatang(formData.price),
        image_url: formData.image_url
      }

      let savedItem: AdminMenuItem

      if (editingItem) {
        // Update existing item
        const updateData: UpdateMenuItemRequest = {
          ...requestData,
          is_available: formData.is_available
        }
        savedItem = await adminMenuAPI.updateItem(editingItem.id, updateData)
      } else {
        // Create new item
        savedItem = await adminMenuAPI.createItem(requestData)
      }

      // Upload image if provided
      if (imageFile) {
        setUploading(true)
        try {
          const uploadResult = await adminMenuAPI.uploadImage(savedItem.id, imageFile)
          savedItem = uploadResult.item
        } catch (uploadErr) {
          console.error('Error uploading image:', uploadErr)
          alert('บันทึกเมนูสำเร็จ แต่อัพโหลดรูปภาพไม่สำเร็จ')
        } finally {
          setUploading(false)
        }
      }

      // Reset form and reload items
      resetForm()
      await loadMenuItems()
      
      alert(editingItem ? 'แก้ไขเมนูสำเร็จ!' : 'เพิ่มเมนูใหม่สำเร็จ!')
      
    } catch (err) {
      console.error('Error saving menu item:', err)
      alert('เกิดข้อผิดพลาดในการบันทึก')
    } finally {
      setLoading(false)
    }
  }

  const handleEdit = (item: AdminMenuItem) => {
    setEditingItem(item)
    setFormData({
      name: item.name,
      description: item.description,
      price: utils.satangToBaht(item.price),
      image_url: item.image_url,
      is_available: item.is_available
    })
    setImageFile(null)
    setShowForm(true)
  }

  const handleDelete = async (item: AdminMenuItem) => {
    if (!confirm(`คุณต้องการลบเมนู "${item.name}" หรือไม่?`)) {
      return
    }

    try {
      await adminMenuAPI.deleteItem(item.id)
      await loadMenuItems()
      alert('ลบเมนูสำเร็จ!')
    } catch (err) {
      console.error('Error deleting menu item:', err)
      alert('เกิดข้อผิดพลาดในการลบเมนู')
    }
  }

  const toggleAvailability = async (item: AdminMenuItem) => {
    try {
      const updateData: UpdateMenuItemRequest = {
        name: item.name,
        description: item.description,
        price: item.price,
        image_url: item.image_url,
        is_available: !item.is_available
      }
      
      await adminMenuAPI.updateItem(item.id, updateData)
      await loadMenuItems()
    } catch (err) {
      console.error('Error updating availability:', err)
      alert('เกิดข้อผิดพลาดในการเปลี่ยนสถานะ')
    }
  }

  const resetForm = () => {
    setFormData({
      name: '',
      description: '',
      price: 0,
      image_url: '',
      is_available: true
    })
    setImageFile(null)
    setEditingItem(null)
    setShowForm(false)
  }

  const handleImageChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      if (file.size > 10 * 1024 * 1024) { // 10MB limit
        alert('ไฟล์รูปภาพต้องมีขนาดไม่เกิน 10MB')
        return
      }
      
      if (!file.type.startsWith('image/')) {
        alert('กรุณาเลือกไฟล์รูปภาพเท่านั้น')
        return
      }
      
      setImageFile(file)
    }
  }

  if (loading && menuItems.length === 0) {
    return (
      <div className="admin-menu-page">
        <div className="loading">กำลังโหลด...</div>
      </div>
    )
  }

  return (
    <div className="admin-menu-page">
      <div className="admin-header">
        <h1>จัดการเมนูอาหาร</h1>
        <div className="header-actions">
          <button 
            className="btn btn-primary"
            onClick={() => setShowForm(true)}
            disabled={loading}
          >
            เพิ่มเมนูใหม่
          </button>
          <button 
            className="btn btn-secondary"
            onClick={loadMenuItems}
            disabled={loading}
          >
            รีเฟรช
          </button>
        </div>
      </div>

      {error && (
        <div className="error-message">
          <p>เกิดข้อผิดพลาด: {error}</p>
          <button onClick={loadMenuItems}>ลองใหม่</button>
        </div>
      )}

      {showForm && (
        <div className="modal-overlay">
          <div className="modal">
            <div className="modal-header">
              <h2>{editingItem ? 'แก้ไขเมนู' : 'เพิ่มเมนูใหม่'}</h2>
              <button className="close-btn" onClick={resetForm}>×</button>
            </div>
            
            <form onSubmit={handleSubmit} className="menu-form">
              <div className="form-group">
                <label htmlFor="name">ชื่อเมนู *</label>
                <input
                  type="text"
                  id="name"
                  value={formData.name}
                  onChange={(e) => setFormData({...formData, name: e.target.value})}
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="description">รายละเอียด</label>
                <textarea
                  id="description"
                  value={formData.description}
                  onChange={(e) => setFormData({...formData, description: e.target.value})}
                  rows={3}
                />
              </div>

              <div className="form-group">
                <label htmlFor="price">ราคา (บาท) *</label>
                <input
                  type="number"
                  id="price"
                  value={formData.price}
                  onChange={(e) => setFormData({...formData, price: parseFloat(e.target.value) || 0})}
                  min="0"
                  step="0.01"
                  required
                />
              </div>

              <div className="form-group">
                <label htmlFor="image_url">URL รูปภาพ</label>
                <input
                  type="text"
                  id="image_url"
                  value={formData.image_url}
                  onChange={(e) => setFormData({...formData, image_url: e.target.value})}
                />
              </div>

              <div className="form-group">
                <label htmlFor="image_file">อัพโหลดรูปภาพ</label>
                <input
                  type="file"
                  id="image_file"
                  accept="image/*"
                  onChange={handleImageChange}
                />
                {imageFile && <p className="file-info">ไฟล์ที่เลือก: {imageFile.name}</p>}
              </div>

              {editingItem && (
                <div className="form-group">
                  <label>
                    <input
                      type="checkbox"
                      checked={formData.is_available}
                      onChange={(e) => setFormData({...formData, is_available: e.target.checked})}
                    />
                    พร้อมขาย
                  </label>
                </div>
              )}

              <div className="form-actions">
                <button 
                  type="submit" 
                  className="btn btn-primary"
                  disabled={loading || uploading}
                >
                  {loading || uploading ? 'กำลังบันทึก...' : (editingItem ? 'บันทึกการแก้ไข' : 'เพิ่มเมนู')}
                </button>
                <button 
                  type="button" 
                  className="btn btn-secondary"
                  onClick={resetForm}
                  disabled={loading || uploading}
                >
                  ยกเลิก
                </button>
              </div>
            </form>
          </div>
        </div>
      )}

      <div className="menu-grid">
        {menuItems.map(item => (
          <div key={item.id} className={`menu-card ${!item.is_available ? 'unavailable' : ''}`}>
            <div className="menu-image">
              {item.image_url ? (
                <img 
                  src={utils.getImageUrl(item.image_url)} 
                  alt={item.name}
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = '/images/placeholder.svg'
                  }}
                />
              ) : (
                <div className="no-image">ไม่มีรูปภาพ</div>
              )}
            </div>
            
            <div className="menu-info">
              <h3>{item.name}</h3>
              <p className="description">{item.description || 'ไม่มีรายละเอียด'}</p>
              <p className="price">{utils.formatPrice(item.price)}</p>
              <p className={`status ${item.is_available ? 'available' : 'unavailable'}`}>
                {item.is_available ? 'พร้อมขาย' : 'ไม่พร้อมขาย'}
              </p>
            </div>
            
            <div className="menu-actions">
              <button 
                className="btn btn-small btn-primary"
                onClick={() => handleEdit(item)}
              >
                แก้ไข
              </button>
              <button 
                className={`btn btn-small ${item.is_available ? 'btn-warning' : 'btn-success'}`}
                onClick={() => toggleAvailability(item)}
              >
                {item.is_available ? 'ปิดขาย' : 'เปิดขาย'}
              </button>
              <button 
                className="btn btn-small btn-danger"
                onClick={() => handleDelete(item)}
              >
                ลบ
              </button>
            </div>
          </div>
        ))}
      </div>

      {menuItems.length === 0 && !loading && (
        <div className="empty-state">
          <p>ยังไม่มีเมนูในระบบ</p>
          <button 
            className="btn btn-primary"
            onClick={() => setShowForm(true)}
          >
            เพิ่มเมนูแรก
          </button>
        </div>
      )}
    </div>
  )
}

export default AdminMenuPage
