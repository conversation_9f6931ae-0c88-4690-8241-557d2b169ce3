package ports

import (
	"context"

	order "github.com/micksudev/go-qr-order/internal/core/domain"
)

type TableRepo interface {
	FindByCode(ctx context.Context, code string) (*order.Table, error)
	UpdateStatus(ctx context.Context, tableID int64, status order.TableStatus) error
	ListAll(ctx context.Context) ([]order.Table, error)
}

type MenuRepo interface {
	FindByID(ctx context.Context, id int64) (*order.MenuItem, error)
	ListActive(ctx context.Context) ([]order.MenuItem, error)
	Create(ctx context.Context, item *order.MenuItem) (*order.MenuItem, error)
	Update(ctx context.Context, item *order.MenuItem) (*order.MenuItem, error)
	Delete(ctx context.Context, id int64) error
	ListAll(ctx context.Context) ([]order.MenuItem, error)
}

type OrderRepo interface {
	CreateDraft(ctx context.Context, tableID int64) (*order.Order, error)
	Save(ctx context.Context, o *order.Order) error
	GetByID(ctx context.Context, id int64) (*order.Order, error)
	GetDraftByTable(ctx context.Context, tableID int64) (*order.Order, error)
	ListAll(ctx context.Context) ([]order.Order, error)
	ListByStatus(ctx context.Context, status order.OrderStatus) ([]order.Order, error)
}

type EventBus interface {
	PublishOrderSubmitted(ctx context.Context, o *order.Order) error
	PublishOrderStatusChanged(ctx context.Context, o *order.Order) error
}

type Repos interface {
	Tables() TableRepo
	Menus() MenuRepo
	Orders() OrderRepo
}

type UnitOfWork interface {
	Do(ctx context.Context, fn func(r Repos) error) error
}
