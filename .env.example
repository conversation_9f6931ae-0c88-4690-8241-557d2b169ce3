# Database Configuration
POSTGRES_DB=qr_order
POSTGRES_USER=qr_user
POSTGRES_PASSWORD=qr_password
DATABASE_URL=********************************************/qr_order?sslmode=disable

# Backend Configuration
PORT=5050
CORS_ORIGINS=http://localhost:3000,http://localhost:5173

# Frontend Configuration (for development)
VITE_API_BASE_URL=http://localhost:5050
VITE_WS_URL=ws://localhost:5050/ws

# Production URLs (when using nginx proxy)
# VITE_API_BASE_URL=http://localhost/api
# VITE_WS_URL=ws://localhost/ws
