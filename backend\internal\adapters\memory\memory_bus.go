// internal/adapters/event/memory_bus.go
package memory

import (
	"context"
	"encoding/json"
	"sync"

	order "github.com/micksudev/go-qr-order/internal/core/domain"
)

type Subscriber func(topic string, payload []byte)

type MemoryBus struct {
	mu   sync.RWMutex
	subs map[string][]Subscriber // topic -> handlers
}

func NewMemoryBus() *MemoryBus {
	return &MemoryBus{subs: make(map[string][]Subscriber)}
}

func (b *MemoryBus) Subscribe(topic string, fn Subscriber) {
	b.mu.Lock()
	defer b.mu.Unlock()
	b.subs[topic] = append(b.subs[topic], fn)
}

func (b *MemoryBus) publish(topic string, v any) error {
	b.mu.RLock()
	subs := append([]Subscriber(nil), b.subs[topic]...)
	b.mu.RUnlock()

	data, err := json.Marshal(v)
	if err != nil {
		return err
	}

	for _, fn := range subs {
		// fire-and-forget
		go fn(topic, data)
	}
	return nil
}

// implement ports.EventBus
func (b *MemoryBus) PublishOrderSubmitted(ctx context.Context, o *order.Order) error {
	type evt struct {
		Type    string      `json:"type"`
		OrderID int64       `json:"order_id"`
		TableID int64       `json:"table_id"`
		Status  string      `json:"status"`
		Total   int64       `json:"total_satang"`
		Items   interface{} `json:"items,omitempty"` // จะใส่เต็ม/บางส่วนก็ได้
	}
	return b.publish("order.submitted", evt{
		Type: "order.submitted", OrderID: o.ID, TableID: o.TableID,
		Status: string(o.Status), Total: o.Total, Items: o.Items,
	})
}

func (b *MemoryBus) PublishOrderStatusChanged(ctx context.Context, o *order.Order) error {
	type evt struct {
		Type    string `json:"type"`
		OrderID int64  `json:"order_id"`
		TableID int64  `json:"table_id"`
		Status  string `json:"status"`
		Total   int64  `json:"total_satang"`
	}
	return b.publish("order.status.changed", evt{
		Type: "order.status.changed", OrderID: o.ID, TableID: o.TableID,
		Status: string(o.Status), Total: o.Total,
	})
}
