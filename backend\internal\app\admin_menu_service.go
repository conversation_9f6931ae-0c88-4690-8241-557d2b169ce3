package app

import (
	"context"
	"errors"

	order "github.com/micksudev/go-qr-order/internal/core/domain"
	"github.com/micksudev/go-qr-order/internal/ports"
)

type AdminMenuService struct {
	uow   ports.UnitOfWork
	menus ports.MenuRepo
}

func NewAdminMenuService(uow ports.UnitOfWork, menus ports.MenuRepo) *AdminMenuService {
	return &AdminMenuService{uow: uow, menus: menus}
}

func (s *AdminMenuService) CreateMenuItem(ctx context.Context, name, description, imageURL string, price int64) (*order.MenuItem, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if price <= 0 {
		return nil, errors.New("price must be positive")
	}

	var result *order.MenuItem
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		item := &order.MenuItem{
			Name:        name,
			Description: description,
			ImageURL:    imageURL,
			Price:       price,
			IsAvailable: true, // default to available
		}
		
		created, err := r.Menus().Create(ctx, item)
		if err != nil {
			return err
		}
		result = created
		return nil
	})
	
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *AdminMenuService) UpdateMenuItem(ctx context.Context, id int64, name, description, imageURL string, price int64, isAvailable bool) (*order.MenuItem, error) {
	if name == "" {
		return nil, errors.New("name is required")
	}
	if price <= 0 {
		return nil, errors.New("price must be positive")
	}

	var result *order.MenuItem
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		// Check if item exists
		existing, err := r.Menus().FindByID(ctx, id)
		if err != nil {
			return errors.New("menu item not found")
		}
		
		// Update the item
		existing.Name = name
		existing.Description = description
		existing.ImageURL = imageURL
		existing.Price = price
		existing.IsAvailable = isAvailable
		
		updated, err := r.Menus().Update(ctx, existing)
		if err != nil {
			return err
		}
		result = updated
		return nil
	})
	
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *AdminMenuService) DeleteMenuItem(ctx context.Context, id int64) error {
	return s.uow.Do(ctx, func(r ports.Repos) error {
		// Check if item exists
		_, err := r.Menus().FindByID(ctx, id)
		if err != nil {
			return errors.New("menu item not found")
		}
		
		return r.Menus().Delete(ctx, id)
	})
}

func (s *AdminMenuService) GetMenuItem(ctx context.Context, id int64) (*order.MenuItem, error) {
	var result *order.MenuItem
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		item, err := r.Menus().FindByID(ctx, id)
		if err != nil {
			return errors.New("menu item not found")
		}
		result = item
		return nil
	})
	
	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *AdminMenuService) ListAllMenuItems(ctx context.Context) ([]order.MenuItem, error) {
	var result []order.MenuItem
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		items, err := r.Menus().ListAll(ctx)
		if err != nil {
			return err
		}
		result = items
		return nil
	})
	
	if err != nil {
		return nil, err
	}
	return result, nil
}
