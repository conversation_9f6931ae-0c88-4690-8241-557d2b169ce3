package app

import (
	"context"
	"errors"

	order "github.com/micksudev/go-qr-order/internal/core/domain"
	"github.com/micksudev/go-qr-order/internal/ports"
)

type OrderService struct {
	uow    ports.UnitOfWork
	tables ports.TableRepo
	menus  ports.MenuRepo
	orders ports.OrderRepo
	bus    ports.EventBus
}

func NewOrderService(uow ports.UnitOfWork, tr ports.TableRepo, mr ports.MenuRepo, or ports.OrderRepo, eb ports.EventBus) *OrderService {
	return &OrderService{uow: uow, tables: tr, menus: mr, orders: or, bus: eb}
}

func (s *OrderService) AddItem(ctx context.Context, tableCode string, menuItemID int64, qty int) (*order.Order, error) {
	if qty <= 0 {
		return nil, errors.New("invalid quantity")
	}

	var out *order.Order
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		tbl, err := r.Tables().FindByCode(ctx, tableCode)
		if err != nil || tbl == nil {
			return errors.New("table not found")
		}

		ord, err := r.Orders().GetDraftByTable(ctx, tbl.ID)
		if err != nil {
			return errors.New("failed to get draft order")
		}
		if ord == nil {
			ord, err = r.Orders().CreateDraft(ctx, tbl.ID)
			if err != nil {
				return errors.New("failed to create draft order")
			}
		}

		mi, err := r.Menus().FindByID(ctx, menuItemID)
		if err != nil || mi == nil {
			return errors.New("menu item not found")
		}
		if !mi.IsAvailable {
			return errors.New("menu item unavailable")
		}

		merged := false
		for i := range ord.Items {
			if ord.Items[i].MenuItemID == mi.ID {
				ord.Items[i].Qty += qty
				merged = true
				break
			}
		}
		if !merged {
			ord.Items = append(ord.Items, order.OrderItem{
				MenuItemID: mi.ID,
				Name:       mi.Name,
				UnitPrice:  mi.Price,
				Qty:        qty,
			})
		}

		var sum int64
		for _, it := range ord.Items {
			sum += int64(it.Qty) * it.UnitPrice
		}
		ord.Total = sum
		ord.Status = order.StatusDraft

		if err := r.Orders().Save(ctx, ord); err != nil {
			return err
		}
		out = ord
		return nil
	})
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (s *OrderService) OpenTable(ctx context.Context, tableCode string) (*order.Table, error) {
	var out *order.Table
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		tbl, err := r.Tables().FindByCode(ctx, tableCode)
		if err != nil || tbl == nil {
			return errors.New("table not found")
		}

		// ensure there is a draft order
		ord, err := r.Orders().GetDraftByTable(ctx, tbl.ID)
		if err != nil {
			return err
		}
		if ord == nil {
			if _, err := r.Orders().CreateDraft(ctx, tbl.ID); err != nil {
				return err
			}
		}

		out = tbl
		return nil
	})
	if err != nil {
		return nil, err
	}
	return out, nil
}

func (s *OrderService) SubmitOrder(ctx context.Context, tableCode string) (*order.Order, error) {
	var out *order.Order
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		tbl, err := r.Tables().FindByCode(ctx, tableCode)
		if err != nil {
			return err
		}

		ord, err := r.Orders().GetDraftByTable(ctx, tbl.ID)
		if err != nil {
			return err
		}
		if ord == nil {
			return errors.New("draft order not found")
		}
		if ord.Status != order.StatusDraft {
			return errors.New("order not in DRAFT")
		}
		if len(ord.Items) == 0 {
			return errors.New("empty order")
		}

		var sum int64
		for _, it := range ord.Items {
			sum += int64(it.Qty) * it.UnitPrice
		}
		ord.Total = sum
		ord.Status = order.StatusSubmitted

		if err := r.Orders().Save(ctx, ord); err != nil {
			return err
		}

		out = ord
		return nil
	})
	if err != nil {
		return nil, err
	}

	if s.bus != nil {
		_ = s.bus.PublishOrderSubmitted(ctx, out)
	}
	return out, nil
}

func (s *OrderService) AcceptOrder(ctx context.Context, orderID int64) error {
	var updatedOrder *order.Order
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		ord, err := r.Orders().GetByID(ctx, orderID)
		if err != nil {
			return err
		}
		if ord == nil {
			return errors.New("order not found")
		}
		if ord.Status != order.StatusSubmitted {
			return errors.New("must be SUBMITTED to accept")
		}
		ord.Status = order.StatusAccepted
		if err := r.Orders().Save(ctx, ord); err != nil {
			return err
		}
		updatedOrder = ord
		return nil
	})

	if err == nil && s.bus != nil && updatedOrder != nil {
		_ = s.bus.PublishOrderStatusChanged(ctx, updatedOrder)
	}
	return err
}

func (s *OrderService) Cancel(ctx context.Context, orderID int64) error {
	var updatedOrder *order.Order
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		ord, err := r.Orders().GetByID(ctx, orderID)
		if err != nil {
			return err
		}
		if ord == nil {
			return errors.New("order not found")
		}
		switch ord.Status {
		case order.StatusServed, order.StatusCanceled:
			return errors.New("cannot cancel in current state")
		}
		ord.Status = order.StatusCanceled
		if err := r.Orders().Save(ctx, ord); err != nil {
			return err
		}
		updatedOrder = ord
		return nil
	})

	if err == nil && s.bus != nil && updatedOrder != nil {
		_ = s.bus.PublishOrderStatusChanged(ctx, updatedOrder)
	}
	return err
}

func (s *OrderService) MarkPreparing(ctx context.Context, orderID int64) error {
	var updatedOrder *order.Order
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		ord, err := r.Orders().GetByID(ctx, orderID)
		if err != nil {
			return err
		}
		if ord == nil {
			return errors.New("order not found")
		}
		if ord.Status != order.StatusAccepted {
			return errors.New("must be ACCEPTED to start preparing")
		}
		ord.Status = order.StatusPreparing
		if err := r.Orders().Save(ctx, ord); err != nil {
			return err
		}
		updatedOrder = ord
		return nil
	})

	if err == nil && s.bus != nil && updatedOrder != nil {
		_ = s.bus.PublishOrderStatusChanged(ctx, updatedOrder)
	}
	return err
}

func (s *OrderService) MarkReady(ctx context.Context, orderID int64) error {
	var updatedOrder *order.Order
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		ord, err := r.Orders().GetByID(ctx, orderID)
		if err != nil {
			return err
		}
		if ord == nil {
			return errors.New("order not found")
		}
		if ord.Status != order.StatusPreparing {
			return errors.New("must be PREPARING to mark ready")
		}
		ord.Status = order.StatusReady
		if err := r.Orders().Save(ctx, ord); err != nil {
			return err
		}
		updatedOrder = ord
		return nil
	})

	if err == nil && s.bus != nil && updatedOrder != nil {
		_ = s.bus.PublishOrderStatusChanged(ctx, updatedOrder)
	}
	return err
}

func (s *OrderService) Serve(ctx context.Context, orderID int64) error {
	var updatedOrder *order.Order
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		ord, err := r.Orders().GetByID(ctx, orderID)
		if err != nil {
			return err
		}
		if ord == nil {
			return errors.New("order not found")
		}
		if ord.Status != order.StatusReady {
			return errors.New("must be READY to serve")
		}
		ord.Status = order.StatusServed
		if err := r.Orders().Save(ctx, ord); err != nil {
			return err
		}
		updatedOrder = ord
		return nil
	})

	if err == nil && s.bus != nil && updatedOrder != nil {
		_ = s.bus.PublishOrderStatusChanged(ctx, updatedOrder)
	}
	return err
}
