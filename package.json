{"name": "", "version": "0.0.0", "description": "", "main": "index.js", "scripts": {"test": "mocha -R spec"}, "devDependencies": {"babel-cli": "^6.9.0", "babel-plugin-add-module-exports": "^0.2.1", "babel-plugin-transform-regenerator": "^6.9.0", "babel-preset-es2015": "^6.9.0", "bake-cli": "^0.1.6", "eslint": "^2.10.2", "eslint-config-standard": "^5.3.1", "eslint-plugin-promise": "^1.1.0", "eslint-plugin-standard": "^1.3.2", "mocha": "^2.5.2", "standard-version": "^2.2.1", "watchd": "github:mklabs/watchd"}, "bake": {"description": "Scaffold a basic ES6 setup", "scripts": {"start": "echo Starting generation of default template", "prestart": "echo prestart", "poststart": "echo poststart", "install": "npm install --loglevel warn --cache-min Infinity", "preinstall": "echo Installing dependencies ...", "postinstall": "npm ls --depth 0"}}}