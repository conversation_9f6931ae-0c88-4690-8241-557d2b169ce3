// internal/adapters/memory/orders.go
package memory

import (
	"context"
	"sync"

	order "github.com/micksudev/go-qr-order/internal/core/domain"
	"github.com/micksudev/go-qr-order/internal/ports"
)

var _ ports.OrderRepo = (*Orders)(nil)

type Orders struct {
	mu           sync.RWMutex
	draftByTable map[int64]*order.Order // key = tableID
	byID         map[int64]*order.Order
	nextID       int64
}

func NewOrders() *Orders {
	return &Orders{
		draftByTable: make(map[int64]*order.Order),
		byID:         make(map[int64]*order.Order),
		nextID:       1,
	}
}

func (r *Orders) GetDraftByTable(ctx context.Context, tableID int64) (*order.Order, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.draftByTable[tableID], nil
}

func (r *Orders) CreateDraft(ctx context.Context, tableID int64) (*order.Order, error) {
	r.mu.Lock()
	defer r.mu.Unlock()
	o := &order.Order{ID: r.nextID, TableID: tableID, Status: order.StatusDraft}
	r.nextID++
	r.draftByTable[tableID] = o
	r.byID[o.ID] = o
	return o, nil
}

func (r *Orders) Save(ctx context.Context, o *order.Order) error {
	r.mu.Lock()
	defer r.mu.Unlock()
	r.byID[o.ID] = o
	if o.Status == order.StatusDraft {
		r.draftByTable[o.TableID] = o
	} else {
		delete(r.draftByTable, o.TableID)
	}
	return nil
}

func (r *Orders) GetByID(ctx context.Context, id int64) (*order.Order, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()
	return r.byID[id], nil
}

func (r *Orders) ListAll(ctx context.Context) ([]order.Order, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	orders := make([]order.Order, 0, len(r.byID))
	for _, o := range r.byID {
		orders = append(orders, *o)
	}

	return orders, nil
}

func (r *Orders) ListByStatus(ctx context.Context, status order.OrderStatus) ([]order.Order, error) {
	r.mu.RLock()
	defer r.mu.RUnlock()

	var orders []order.Order
	for _, o := range r.byID {
		if o.Status == status {
			orders = append(orders, *o)
		}
	}

	return orders, nil
}
