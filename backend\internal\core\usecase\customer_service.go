package usecase

import (
	"context"
	"errors"

	order "github.com/micksudev/go-qr-order/internal/core/domain"
	"github.com/micksudev/go-qr-order/internal/ports"
)

type CustomerService struct {
	tables ports.TableRepo
	menus  ports.MenuRepo
	orders ports.OrderRepo
	bus    ports.EventBus
}

func NewCustomerService(tables ports.TableRepo, menus ports.MenuRepo, orders ports.OrderRepo, bus ports.EventBus) *CustomerService {
	return &CustomerService{tables: tables, menus: menus, orders: orders, bus: bus}
}

// AddItem implements ports.CustomerOrderingPort.
func (c *CustomerService) AddItem(ctx context.Context, tableCode string, menuItemID int64, qty int) (*order.Order, error) {
	if qty <= 0 {
		return nil, errors.New("invalid quantity")
	}

	tbl, err := c.tables.FindByCode(ctx, tableCode)
	if err != nil || tbl == nil {
		return nil, errors.New("table not found")
	}

	ord, err := c.orders.GetDraftByTable(ctx, tbl.ID)
	if err != nil {
		return nil, errors.New("failed to get draft order")
	}

	if ord == nil {
		ord, err = c.orders.CreateDraft(ctx, tbl.ID)
		if err != nil {
			return nil, errors.New("failed to create draft order")
		}
	}

	mi, err := c.menus.FindByID(ctx, menuItemID)
	if err != nil || mi == nil {
		return nil, errors.New("menu item not found")
	}
	if !mi.IsAvailable {
		return nil, errors.New("menu item unavailable")
	}

	merged := false
	for i := range ord.Items {
		if ord.Items[i].MenuItemID == mi.ID {
			ord.Items[i].Qty += qty
			merged = true
			break
		}
	}
	if !merged {
		ord.Items = append(ord.Items, order.OrderItem{
			MenuItemID: mi.ID,
			Name:       mi.Name,
			UnitPrice:  mi.Price,
			Qty:        qty,
		})
	}

	var sum int64
	for _, it := range ord.Items {
		sum += int64(it.Qty) * it.UnitPrice
	}
	ord.Total = sum
	ord.Status = order.StatusDraft

	if err := c.orders.Save(ctx, ord); err != nil {
		return nil, err
	}
	return ord, nil
}

// OpenTable implements ports.CustomerOrderingPort.
func (c *CustomerService) OpenTable(ctx context.Context, tableCode string) (*order.Table, error) {
	tbl, err := c.tables.FindByCode(ctx, tableCode)
	if err != nil {
		return nil, err
	}

	ord, err := c.orders.GetDraftByTable(ctx, tbl.ID)
	if err != nil {
		return nil, err
	}
	if ord == nil {
		return nil, errors.New("draft order not found")
	}

	if err := c.orders.Save(ctx, ord); err != nil {
		return nil, err
	}

	if c.bus != nil {
		_ = c.bus.PublishOrderSubmitted(ctx, ord)
	}
	return ord.Table, nil
}

// SubmitOrder implements ports.CustomerOrderingPort.
func (c *CustomerService) SubmitOrder(ctx context.Context, tableCode string) (*order.Order, error) {
	tbl, err := c.tables.FindByCode(ctx, tableCode)
	if err != nil {
		return nil, err
	}

	ord, err := c.orders.GetDraftByTable(ctx, tbl.ID)
	if err != nil {
		return nil, err
	}
	if ord == nil {
		return nil, errors.New("draft order not found")
	}
	if ord.Status != order.StatusDraft {
		return nil, errors.New("order status is not DRAFT")
	}
	if len(ord.Items) == 0 {
		return nil, errors.New("empty order")
	}

	var sum int64
	for _, it := range ord.Items {
		sum += int64(it.Qty) * it.UnitPrice
	}
	ord.Total = sum

	ord.Status = order.StatusSubmitted

	if err := c.orders.Save(ctx, ord); err != nil {
		return nil, err
	}

	if c.bus != nil {
		_ = c.bus.PublishOrderSubmitted(ctx, ord)
	}
	return ord, nil
}

var _ ports.CustomerOrderingPort = (*CustomerService)(nil)
