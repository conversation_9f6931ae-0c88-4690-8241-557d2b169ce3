package app

import (
	"context"

	order "github.com/micksudev/go-qr-order/internal/core/domain"
	"github.com/micksudev/go-qr-order/internal/ports"
)

type AdminOrderService struct {
	uow    ports.UnitOfWork
	orders ports.OrderRepo
	tables ports.TableRepo
}

func NewAdminOrderService(uow ports.UnitOfWork, orders ports.OrderRepo, tables ports.TableRepo) *AdminOrderService {
	return &AdminOrderService{uow: uow, orders: orders, tables: tables}
}

func (s *AdminOrderService) ListAllOrders(ctx context.Context) ([]order.Order, error) {
	var result []order.Order
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		orders, err := r.Orders().ListAll(ctx)
		if err != nil {
			return err
		}
		result = orders
		return nil
	})

	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *AdminOrderService) ListOrdersByStatus(ctx context.Context, status order.OrderStatus) ([]order.Order, error) {
	var result []order.Order
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		orders, err := r.Orders().ListByStatus(ctx, status)
		if err != nil {
			return err
		}
		result = orders
		return nil
	})

	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *AdminOrderService) GetOrder(ctx context.Context, id int64) (*order.Order, error) {
	var result *order.Order
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		order, err := r.Orders().GetByID(ctx, id)
		if err != nil {
			return err
		}
		result = order
		return nil
	})

	if err != nil {
		return nil, err
	}
	return result, nil
}

func (s *AdminOrderService) ListAllTables(ctx context.Context) ([]order.Table, error) {
	var result []order.Table
	err := s.uow.Do(ctx, func(r ports.Repos) error {
		tables, err := r.Tables().ListAll(ctx)
		if err != nil {
			return err
		}
		result = tables
		return nil
	})

	if err != nil {
		return nil, err
	}
	return result, nil
}
