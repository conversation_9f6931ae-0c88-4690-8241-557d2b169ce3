// internal/adapters/ws/handler.go
package ws

import (
	"net/http"
	"sync"

	"github.com/gorilla/websocket"
)

type Conn struct {
	ws *websocket.Conn
	mu sync.Mutex
}

func (c *Conn) Write(p []byte) error {
	c.mu.Lock()
	defer c.mu.Unlock()
	return c.ws.WriteMessage(websocket.TextMessage, p)
}

var upgrader = websocket.Upgrader{
	CheckOrigin: func(r *http.Request) bool { return true }, // TODO: ปรับ origin ให้ปลอดภัย
}
