services:
  # PostgreSQL Database
  postgres:
    image: postgres:15-alpine
    container_name: qr-order-db
    environment:
      POSTGRES_DB: qr_order
      POSTGRES_USER: qr_user
      POSTGRES_PASSWORD: qr_password
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5432:5432"
    networks:
      - qr-order-network

  # Backend API
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: qr-order-backend
    environment:
      - PORT=5050
      - DATABASE_URL=********************************************/qr_order?sslmode=disable
      - CORS_ORIGINS=http://localhost:3000,http://localhost:5173,http://frontend
    ports:
      - "5050:5050"
    volumes:
      - backend_uploads:/root/uploads
    depends_on:
      - postgres
    networks:
      - qr-order-network

  # Frontend
  frontend:
    build:
      context: ./frontend-vite
      dockerfile: Dockerfile
    container_name: qr-order-frontend
    ports:
      - "3000:80"
    networks:
      - qr-order-network

  # Nginx Reverse Proxy (Optional - for production)
  # nginx:
  #   image: nginx:alpine
  #   container_name: qr-order-proxy
  #   ports:
  #     - "80:80"
  #     - "443:443"
  #   volumes:
  #     - ./nginx/nginx.conf:/etc/nginx/nginx.conf
  #     - ./nginx/conf.d:/etc/nginx/conf.d
  #     - backend_uploads:/var/www/uploads
  #   depends_on:
  #     - frontend
  #     - backend
  #   networks:
  #     - qr-order-network
  #   restart: unless-stopped

volumes:
  postgres_data:
    driver: local
  backend_uploads:
    driver: local

networks:
  qr-order-network:
    driver: bridge
