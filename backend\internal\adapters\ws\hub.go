// internal/adapters/ws/hub.go
package ws

import (
	"net/http"
	"sync"
)

type Hub struct {
	mu      sync.RWMutex
	clients map[string]map[*Conn]struct{} // topic -> set of conn
}

type Server struct {
	Hub *Hub
}

func NewServer(h *Hub) *Server { return &Server{Hub: h} }

// GET /ws?topic=order.submitted
func (s *Server) HandleWS(w http.ResponseWriter, r *http.Request) {
	topic := r.URL.Query().Get("topic")
	if topic == "" {
		topic = "order.submitted"
	} // ค่าเริ่มต้น

	conn, err := upgrader.Upgrade(w, r, nil)
	if err != nil {
		return
	}
	c := &Conn{ws: conn}

	s.Hub.Add(topic, c)
	defer func() { s.Hub.Remove(topic, c); _ = conn.Close() }()

	for {
		if _, _, err := conn.ReadMessage(); err != nil {
			return
		}
	}
}

func NewHub() *Hub {
	return &Hub{clients: make(map[string]map[*Conn]struct{})}
}

func (h *Hub) Add(topic string, c *Conn) {
	h.mu.Lock()
	defer h.mu.Unlock()
	m, ok := h.clients[topic]
	if !ok {
		m = make(map[*Conn]struct{})
		h.clients[topic] = m
	}
	m[c] = struct{}{}
}

func (h *Hub) Remove(topic string, c *Conn) {
	h.mu.Lock()
	defer h.mu.Unlock()
	if m, ok := h.clients[topic]; ok {
		delete(m, c)
		if len(m) == 0 {
			delete(h.clients, topic)
		}
	}
}

func (h *Hub) Broadcast(topic string, msg []byte) {
	h.mu.RLock()
	conns := h.clients[topic]
	// copy keys to avoid holding lock during writes
	var list []*Conn
	for c := range conns {
		list = append(list, c)
	}
	h.mu.RUnlock()

	for _, c := range list {
		_ = c.Write(msg) // fire-and-forget (ใน conn มี lock กันเอง)
	}
}
