.admin-page {
  max-width: 1400px;
  margin: 0 auto;
  padding: 20px;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  background-color: #f8f9fa;
  min-height: 100vh;
}

.admin-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding: 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.header-left {
  display: flex;
  flex-direction: column;
  gap: 10px;
}

.admin-header h1 {
  color: #2c3e50;
  font-size: 1.8rem;
  margin: 0;
}

.admin-nav {
  display: flex;
  gap: 15px;
}

.nav-link {
  color: #007bff;
  text-decoration: none;
  padding: 8px 16px;
  border-radius: 6px;
  background: #f8f9fa;
  transition: all 0.2s ease;
  font-size: 14px;
  font-weight: 500;
  border: 1px solid #dee2e6;
}

.nav-link:hover {
  background: #007bff;
  color: white;
  transform: translateY(-1px);
  box-shadow: 0 2px 4px rgba(0,123,255,0.3);
}

.connection-status {
  display: flex;
  align-items: center;
}

.status-indicator {
  padding: 8px 16px;
  border-radius: 20px;
  font-weight: bold;
  font-size: 0.9rem;
}

.status-indicator.connected {
  background: #d4edda;
  color: #155724;
}

.status-indicator.disconnected {
  background: #f8d7da;
  color: #721c24;
}

.stats-bar {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
  gap: 20px;
  margin-bottom: 30px;
}

.stat-item {
  background: white;
  padding: 20px;
  border-radius: 15px;
  text-align: center;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.stat-number {
  display: block;
  font-size: 2rem;
  font-weight: bold;
  color: #3498db;
  margin-bottom: 5px;
}

.stat-label {
  color: #7f8c8d;
  font-size: 0.9rem;
}

.orders-container {
  margin-bottom: 30px;
}

.no-orders {
  text-align: center;
  padding: 60px 20px;
  background: white;
  border-radius: 15px;
  box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.no-orders p {
  color: #7f8c8d;
  font-size: 1.2rem;
  margin: 0;
}

.orders-grid {
  display: grid;
  grid-template-columns: repeat(auto-fill, minmax(350px, 1fr));
  gap: 20px;
}

.order-card {
  background: white;
  border-radius: 15px;
  padding: 20px;
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.1);
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.order-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 6px 20px rgba(0, 0, 0, 0.15);
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: flex-start;
  margin-bottom: 15px;
}

.table-info h3 {
  color: #2c3e50;
  font-size: 1.3rem;
  margin: 0 0 5px 0;
}

.order-time {
  color: #7f8c8d;
  font-size: 0.85rem;
}

.status-badge {
  color: white;
  padding: 6px 12px;
  border-radius: 15px;
  font-size: 0.8rem;
  font-weight: bold;
  text-align: center;
  min-width: 80px;
}

.order-items {
  margin-bottom: 15px;
  border-top: 1px solid #ecf0f1;
  border-bottom: 1px solid #ecf0f1;
  padding: 15px 0;
}

.order-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 8px 0;
}

.item-name {
  flex: 1;
  color: #2c3e50;
  font-weight: 500;
}

.item-quantity {
  color: #7f8c8d;
  margin: 0 15px;
  font-size: 0.9rem;
}

.item-price {
  color: #e74c3c;
  font-weight: bold;
  min-width: 80px;
  text-align: right;
}

.order-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.total-price {
  font-size: 1.1rem;
  font-weight: bold;
  color: #2c3e50;
}

.action-buttons {
  display: flex;
  gap: 10px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 20px;
  font-size: 0.85rem;
  font-weight: bold;
  cursor: pointer;
  transition: all 0.3s ease;
}

.btn-preparing {
  background: #f39c12;
  color: white;
}

.btn-preparing:hover {
  background: #e67e22;
}

.btn-ready {
  background: #27ae60;
  color: white;
}

.btn-ready:hover {
  background: #229954;
}

.btn-complete {
  background: #95a5a6;
  color: white;
}

.btn-complete:hover {
  background: #7f8c8d;
}

.back-to-home {
  text-align: center;
  padding-top: 20px;
}

.home-link {
  display: inline-block;
  background: #3498db;
  color: white;
  padding: 12px 24px;
  text-decoration: none;
  border-radius: 25px;
  font-weight: bold;
  transition: background 0.3s ease;
}

.home-link:hover {
  background: #2980b9;
}

/* Loading and Error States */
.loading {
  text-align: center;
  padding: 60px 20px;
  font-size: 18px;
  color: #666;
}

.error {
  text-align: center;
  padding: 40px 20px;
  background-color: #f8d7da;
  color: #721c24;
  border-radius: 8px;
  margin: 20px;
}

.error button {
  background-color: #dc3545;
  color: white;
  border: none;
  padding: 10px 20px;
  border-radius: 4px;
  cursor: pointer;
  margin-top: 15px;
}

.error button:hover {
  background-color: #c82333;
}

/* Button Styles for Order Actions */
.btn-cancel {
  background-color: #dc3545;
  color: white;
}

.btn-cancel:hover {
  background-color: #c82333;
}

.order-id {
  font-size: 12px;
  color: #666;
  margin-top: 4px;
}

/* Connection Warning */
.connection-warning {
  background-color: #fff3cd;
  color: #856404;
  padding: 15px;
  border-radius: 8px;
  margin-bottom: 20px;
  text-align: center;
  border: 1px solid #ffeaa7;
}

.connection-warning p {
  margin: 0 0 10px 0;
  font-size: 16px;
  font-weight: 500;
}

.retry-btn {
  background-color: #007bff;
  color: white;
  border: none;
  padding: 8px 16px;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
}

.retry-btn:hover {
  background-color: #0056b3;
}

/* Responsive Design */
@media (max-width: 768px) {
  .admin-page {
    padding: 15px;
  }
  
  .admin-header {
    flex-direction: column;
    gap: 15px;
    text-align: center;
  }
  
  .admin-header h1 {
    font-size: 1.5rem;
  }
  
  .stats-bar {
    grid-template-columns: repeat(2, 1fr);
  }
  
  .orders-grid {
    grid-template-columns: 1fr;
  }
  
  .order-header {
    flex-direction: column;
    gap: 10px;
  }
  
  .order-footer {
    flex-direction: column;
    gap: 15px;
    align-items: stretch;
  }
  
  .action-buttons {
    justify-content: center;
  }
}
