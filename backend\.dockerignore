# Binaries
*.exe
*.exe~
*.dll
*.so
*.dylib
main
main.exe

# Test binary, built with `go test -c`
*.test

# Output of the go coverage tool
*.out

# Dependency directories
vendor/

# Go workspace file
go.work

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log

# Environment files
.env
.env.local
.env.development
.env.test
.env.production

# Uploads directory (will be created in container)
uploads/

# Git
.git/
.gitignore

# Documentation
*.md
README.md

# Docker files
Dockerfile
.dockerignore
