import React, { useState, useEffect } from 'react'
import { useParams } from 'react-router-dom'
import './OrderPage.css'
import { menuAPI, orderAPI, utils } from '../services/api'
import type { Order } from '../services/api'

interface MenuItem {
  id: number
  name: string
  price: number
  description: string
  image_url: string
  category?: string
}

interface CartItem extends MenuItem {
  quantity: number
}

const OrderPage: React.FC = () => {
  const { tableId } = useParams<{ tableId: string }>()
  const [cart, setCart] = useState<CartItem[]>([])
  const [showCart, setShowCart] = useState(false)
  const [menuItems, setMenuItems] = useState<MenuItem[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [, setCurrentOrder] = useState<Order | null>(null)

  // Load menu items from API
  useEffect(() => {
    loadMenuItems()
  }, [])

  const loadMenuItems = async () => {
    try {
      setLoading(true)
      setError(null)
      const items = await menuAPI.getAvailableItems()

      // Convert API items to local format and add categories
      const convertedItems: MenuItem[] = items.map(item => ({
        id: item.id,
        name: item.name,
        price: utils.satangToBaht(item.price),
        description: item.description,
        image_url: item.image_url,
        category: getCategoryFromName(item.name) // Simple categorization
      }))

      setMenuItems(convertedItems)
    } catch (err) {
      setError(err instanceof Error ? err.message : 'Failed to load menu')
      console.error('Error loading menu:', err)
    } finally {
      setLoading(false)
    }
  }

  // Simple categorization based on menu item name
  const getCategoryFromName = (name: string): string => {
    if (name.includes('ผัด') || name.includes('ข้าว')) return 'อาหารจานเดียว'
    if (name.includes('แกง') || name.includes('ต้มยำ')) return 'แกง/ต้มยำ'
    if (name.includes('ส้มตำ') || name.includes('ยำ')) return 'ยำ/สลัด'
    if (name.includes('น้ำ') || name.includes('เครื่องดื่ม')) return 'เครื่องดื่ม'
    return 'อื่นๆ'
  }

  const categories = [...new Set(menuItems.map(item => item.category))]

  const addToCart = async (item: MenuItem) => {
    if (!tableId) {
      alert('ไม่พบรหัสโต๊ะ')
      return
    }

    try {
      // Add item to backend order
      const order = await orderAPI.addItem(tableId, item.id, 1)
      setCurrentOrder(order)

      // Update local cart state
      setCart(prevCart => {
        const existingItem = prevCart.find(cartItem => cartItem.id === item.id)
        if (existingItem) {
          return prevCart.map(cartItem =>
            cartItem.id === item.id
              ? { ...cartItem, quantity: cartItem.quantity + 1 }
              : cartItem
          )
        } else {
          return [...prevCart, { ...item, quantity: 1 }]
        }
      })
    } catch (err) {
      console.error('Error adding item to cart:', err)
      alert('เกิดข้อผิดพลาดในการเพิ่มสินค้า')
    }
  }

  const removeFromCart = (itemId: number) => {
    setCart(prevCart => {
      return prevCart.reduce((acc, cartItem) => {
        if (cartItem.id === itemId) {
          if (cartItem.quantity > 1) {
            acc.push({ ...cartItem, quantity: cartItem.quantity - 1 })
          }
        } else {
          acc.push(cartItem)
        }
        return acc
      }, [] as CartItem[])
    })
  }

  const getTotalPrice = () => {
    return cart.reduce((total, item) => total + (item.price * item.quantity), 0)
  }

  const getTotalItems = () => {
    return cart.reduce((total, item) => total + item.quantity, 0)
  }

  const handleOrder = async () => {
    if (cart.length === 0) {
      alert('กรุณาเลือกอาหารก่อนสั่ง')
      return
    }

    if (!tableId) {
      alert('ไม่พบรหัสโต๊ะ')
      return
    }

    try {
      // Submit order to backend
      const order = await orderAPI.submitOrder(tableId)

      alert(`สั่งอาหารสำเร็จ!\nโต๊ะ: ${tableId}\nหมายเลขออเดอร์: ${order.id}\nยอดรวม: ${utils.formatPrice(order.total_satang)}`)

      // Clear cart and close modal
      setCart([])
      setCurrentOrder(null)
      setShowCart(false)
    } catch (err) {
      console.error('Error submitting order:', err)
      alert('เกิดข้อผิดพลาดในการสั่งอาหาร กรุณาลองใหม่อีกครั้ง')
    }
  }

  if (loading) {
    return (
      <div className="order-page">
        <div className="header">
          <h1>โต๊ะ {tableId}</h1>
        </div>
        <div className="loading">กำลังโหลดเมนู...</div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="order-page">
        <div className="header">
          <h1>โต๊ะ {tableId}</h1>
        </div>
        <div className="error">
          <p>เกิดข้อผิดพลาด: {error}</p>
          <button onClick={loadMenuItems}>ลองใหม่</button>
        </div>
      </div>
    )
  }

  return (
    <div className="order-page">
      <div className="header">
        <h1>โต๊ะ {tableId}</h1>
        <button
          className="cart-button"
          onClick={() => setShowCart(!showCart)}
        >
          ตระกร้า ({getTotalItems()})
        </button>
      </div>

      {showCart && (
        <div className="cart-overlay">
          <div className="cart-modal">
            <div className="cart-header">
              <h2>ตระกร้าสินค้า</h2>
              <button onClick={() => setShowCart(false)}>✕</button>
            </div>
            <div className="cart-items">
              {cart.length === 0 ? (
                <p>ไม่มีสินค้าในตระกร้า</p>
              ) : (
                cart.map(item => (
                  <div key={item.id} className="cart-item">
                    <span>{item.name}</span>
                    <div className="quantity-controls">
                      <button onClick={() => removeFromCart(item.id)}>-</button>
                      <span>{item.quantity}</span>
                      <button onClick={() => addToCart(item)}>+</button>
                    </div>
                    <span>{item.price * item.quantity} บาท</span>
                  </div>
                ))
              )}
            </div>
            <div className="cart-footer">
              <div className="total">รวม: {getTotalPrice()} บาท</div>
              <button className="order-button" onClick={handleOrder}>
                สั่งอาหาร
              </button>
            </div>
          </div>
        </div>
      )}

      <div className="menu">
        {categories.map(category => (
          <div key={category} className="category-section">
            <h2>{category}</h2>
            <div className="menu-grid">
              {menuItems
                .filter(item => item.category === category)
                .map(item => (
                  <div key={item.id} className="menu-item">
                    <div className="item-image">
                      <img src={utils.getImageUrl(item.image_url)} alt={item.name} onError={(e) => {
                        (e.target as HTMLImageElement).src = '/images/placeholder.svg'
                      }} />
                    </div>
                    <div className="item-info">
                      <h3>{item.name}</h3>
                      <p>{item.description}</p>
                      <div className="item-footer">
                        <span className="price">{item.price} บาท</span>
                        <button 
                          className="add-button"
                          onClick={() => addToCart(item)}
                        >
                          เพิ่ม
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

export default OrderPage
