package order

type OrderStatus string

const (
	StatusDraft     OrderStatus = "DRAFT"
	StatusSubmitted OrderStatus = "SUBMITTED"
	StatusAccepted  OrderStatus = "ACCEPTED"
	StatusPreparing OrderStatus = "PREPARING"
	StatusReady     OrderStatus = "READY"
	StatusServed    OrderStatus = "SERVED"
	StatusCanceled  OrderStatus = "CANCELED"
)

func (s OrderStatus) CanTransitTo(next OrderStatus) bool {
	switch s {
	case StatusDraft:
		return next == StatusSubmitted || next == StatusCanceled
	case StatusSubmitted:
		return next == StatusAccepted || next == StatusCanceled
	case StatusAccepted:
		return next == StatusPreparing || next == StatusCanceled
	case StatusPreparing:
		return next == StatusReady || next == StatusCanceled
	case StatusReady:
		return next == StatusServed || next == StatusCanceled
	default:
		return false
	}
}

type Order struct {
	ID      int64
	TableID int64
	Table   *Table

	Status OrderStatus
	Items  []OrderItem
	Total  int64
}

type MenuItem struct {
	ID          int64
	Name        string
	Description string
	Price       int64
	ImageURL    string
	IsAvailable bool
}

type OrderItem struct {
	MenuItemID int64
	Name       string
	UnitPrice  int64
	Qty        int
}

type TableStatus string

const (
	StatusAvailable TableStatus = "AVAILABLE"
	StatusOccupied  TableStatus = "OCCUPIED"
)

type Table struct {
	ID     int64
	Code   string
	Status TableStatus
	Orders []Order
}
