// API service for QR Order system
const API_BASE = 'http://localhost:5050';

// Types
export interface MenuItem {
  id: number;
  name: string;
  description: string;
  price: number;
  image_url: string;
  is_available?: boolean;
}

export interface AdminMenuItem extends MenuItem {
  is_available: boolean;
}

export interface CartItem {
  menu_item_id: number;
  name: string;
  unit_price_satang: number;
  qty: number;
}

export interface Order {
  id: number;
  table_id: number;
  status: string;
  total_satang: number;
  items: CartItem[];
}

export interface Table {
  id: number;
  code: string;
  status: string;
}

export interface CreateMenuItemRequest {
  name: string;
  description: string;
  price: number;
  image_url: string;
}

export interface UpdateMenuItemRequest extends CreateMenuItemRequest {
  is_available: boolean;
}

// Helper function to handle API responses
async function handleResponse<T>(response: Response): Promise<T> {
  if (!response.ok) {
    const error = await response.text();
    throw new Error(`HTTP ${response.status}: ${error}`);
  }
  
  if (response.status === 204) {
    return null as T; // No content
  }
  
  return await response.json();
}

// Public Menu API (for customers)
export const menuAPI = {
  // Get available menu items for customers
  async getAvailableItems(): Promise<MenuItem[]> {
    const response = await fetch(`${API_BASE}/menu`);
    return handleResponse<MenuItem[]>(response);
  }
};

// Cart and Order API
export const orderAPI = {
  // Add item to cart/order
  async addItem(tableCode: string, menuItemId: number, qty: number): Promise<Order> {
    const response = await fetch(`${API_BASE}/cart/items`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        table_code: tableCode,
        menu_item_id: menuItemId,
        qty: qty
      })
    });
    return handleResponse<Order>(response);
  },

  // Submit order
  async submitOrder(tableCode: string): Promise<Order> {
    const response = await fetch(`${API_BASE}/orders/submit`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        table_code: tableCode
      })
    });
    return handleResponse<Order>(response);
  }
};

// Admin Menu Management API
export const adminMenuAPI = {
  // Get all menu items (admin view)
  async getAllItems(): Promise<AdminMenuItem[]> {
    const response = await fetch(`${API_BASE}/admin/menu/`);
    return handleResponse<AdminMenuItem[]>(response);
  },

  // Get single menu item
  async getItem(id: number): Promise<AdminMenuItem> {
    const response = await fetch(`${API_BASE}/admin/menu/${id}`);
    return handleResponse<AdminMenuItem>(response);
  },

  // Create new menu item
  async createItem(data: CreateMenuItemRequest): Promise<AdminMenuItem> {
    const response = await fetch(`${API_BASE}/admin/menu/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });
    return handleResponse<AdminMenuItem>(response);
  },

  // Update menu item
  async updateItem(id: number, data: UpdateMenuItemRequest): Promise<AdminMenuItem> {
    const response = await fetch(`${API_BASE}/admin/menu/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(data)
    });
    return handleResponse<AdminMenuItem>(response);
  },

  // Delete menu item
  async deleteItem(id: number): Promise<void> {
    const response = await fetch(`${API_BASE}/admin/menu/${id}`, {
      method: 'DELETE'
    });
    return handleResponse<void>(response);
  },

  // Upload menu item image
  async uploadImage(id: number, imageFile: File): Promise<{ message: string; image_url: string; item: AdminMenuItem }> {
    const formData = new FormData();
    formData.append('image', imageFile);
    
    const response = await fetch(`${API_BASE}/admin/menu/${id}/upload-image`, {
      method: 'POST',
      body: formData
    });
    return handleResponse<{ message: string; image_url: string; item: AdminMenuItem }>(response);
  }
};

// Admin Order Management API
export const adminOrderAPI = {
  // Get all orders
  async getAllOrders(): Promise<Order[]> {
    const response = await fetch(`${API_BASE}/admin/orders`);
    return handleResponse<Order[]>(response);
  },

  // Get orders by status
  async getOrdersByStatus(status: string): Promise<Order[]> {
    const response = await fetch(`${API_BASE}/admin/orders?status=${encodeURIComponent(status)}`);
    return handleResponse<Order[]>(response);
  },

  // Get single order
  async getOrder(orderId: number): Promise<Order> {
    const response = await fetch(`${API_BASE}/admin/orders/${orderId}`);
    return handleResponse<Order>(response);
  },

  // Get all tables
  async getAllTables(): Promise<Table[]> {
    const response = await fetch(`${API_BASE}/admin/tables`);
    return handleResponse<Table[]>(response);
  },

  // Accept order
  async acceptOrder(orderId: number): Promise<void> {
    const response = await fetch(`${API_BASE}/admin/orders/${orderId}/accept`, {
      method: 'POST'
    });
    return handleResponse<void>(response);
  },

  // Mark order as preparing
  async markPreparing(orderId: number): Promise<void> {
    const response = await fetch(`${API_BASE}/admin/orders/${orderId}/preparing`, {
      method: 'POST'
    });
    return handleResponse<void>(response);
  },

  // Mark order as ready
  async markReady(orderId: number): Promise<void> {
    const response = await fetch(`${API_BASE}/admin/orders/${orderId}/ready`, {
      method: 'POST'
    });
    return handleResponse<void>(response);
  },

  // Mark order as served
  async serve(orderId: number): Promise<void> {
    const response = await fetch(`${API_BASE}/admin/orders/${orderId}/serve`, {
      method: 'POST'
    });
    return handleResponse<void>(response);
  },

  // Cancel order
  async cancelOrder(orderId: number): Promise<void> {
    const response = await fetch(`${API_BASE}/admin/orders/${orderId}/cancel`, {
      method: 'POST'
    });
    return handleResponse<void>(response);
  }
};

// WebSocket connection for real-time updates
export class OrderWebSocket {
  private ws: WebSocket | null = null;
  private reconnectAttempts = 0;
  private maxReconnectAttempts = 5;
  private reconnectDelay = 1000;
  private topic: string;
  private onMessage: (data: any) => void;
  private onConnectionChange: (connected: boolean) => void;

  constructor(
    onMessage: (data: any) => void,
    onConnectionChange: (connected: boolean) => void,
    topic: string = 'order.submitted'
  ) {
    this.onMessage = onMessage;
    this.onConnectionChange = onConnectionChange;
    this.topic = topic;
  }

  connect() {
    try {
      // Connect to specified topic for real-time updates
      this.ws = new WebSocket(`ws://localhost:5050/ws?topic=${this.topic}`);

      this.ws.onopen = () => {
        console.log(`WebSocket connected to ${this.topic} topic`);
        this.reconnectAttempts = 0;
        this.onConnectionChange(true);
      };

      this.ws.onmessage = (event) => {
        try {
          const data = JSON.parse(event.data);
          this.onMessage(data);
        } catch (error) {
          console.error('Error parsing WebSocket message:', error);
        }
      };

      this.ws.onclose = () => {
        console.log('WebSocket disconnected');
        this.onConnectionChange(false);
        this.attemptReconnect();
      };

      this.ws.onerror = (error) => {
        console.error('WebSocket error:', error);
        this.onConnectionChange(false);
      };
    } catch (error) {
      console.error('Error creating WebSocket connection:', error);
      this.onConnectionChange(false);
    }
  }

  private attemptReconnect() {
    if (this.reconnectAttempts < this.maxReconnectAttempts) {
      this.reconnectAttempts++;
      console.log(`Attempting to reconnect... (${this.reconnectAttempts}/${this.maxReconnectAttempts})`);
      
      setTimeout(() => {
        this.connect();
      }, this.reconnectDelay * this.reconnectAttempts);
    }
  }

  disconnect() {
    if (this.ws) {
      this.ws.close();
      this.ws = null;
    }
  }

  isConnected(): boolean {
    return this.ws?.readyState === WebSocket.OPEN;
  }
}

// Multi-topic WebSocket manager for admin
export class AdminWebSocketManager {
  private connections: Map<string, OrderWebSocket> = new Map();
  private messageHandlers: Map<string, (data: any) => void> = new Map();
  private connectionHandler: (connected: boolean) => void;

  constructor(onConnectionChange: (connected: boolean) => void) {
    this.connectionHandler = onConnectionChange;
  }

  subscribe(topic: string, handler: (data: any) => void) {
    this.messageHandlers.set(topic, handler);

    const ws = new OrderWebSocket(
      (data) => {
        const handler = this.messageHandlers.get(data.type);
        if (handler) {
          handler(data);
        }
      },
      (connected) => {
        // Update connection status based on any connection
        const anyConnected = Array.from(this.connections.values()).some(ws => ws.isConnected());
        this.connectionHandler(anyConnected || connected);
      },
      topic
    );

    this.connections.set(topic, ws);
    ws.connect();
  }

  disconnect() {
    this.connections.forEach(ws => ws.disconnect());
    this.connections.clear();
    this.messageHandlers.clear();
  }

  isConnected(): boolean {
    return Array.from(this.connections.values()).some(ws => ws.isConnected());
  }
}

// Utility functions
export const utils = {
  // Convert satang to baht
  satangToBaht(satang: number): number {
    return satang / 100;
  },

  // Convert baht to satang
  bahtToSatang(baht: number): number {
    return Math.round(baht * 100);
  },

  // Format price for display
  formatPrice(satang: number): string {
    return `฿${(satang / 100).toFixed(2)}`;
  },

  // Get full image URL
  getImageUrl(imagePath: string): string {
    if (!imagePath) return '/images/placeholder.svg';
    if (imagePath.startsWith('http')) return imagePath;
    return `${API_BASE}${imagePath}`;
  }
};
