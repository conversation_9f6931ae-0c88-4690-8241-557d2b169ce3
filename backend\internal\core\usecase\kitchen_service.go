package usecase

import (
	"context"
	"errors"

	order "github.com/micksudev/go-qr-order/internal/core/domain"
	"github.com/micksudev/go-qr-order/internal/ports"
)

type KitchenService struct {
	orders ports.OrderRepo
}

func NewKitchenService(orders ports.OrderRepo) *KitchenService {
	return &KitchenService{orders: orders}
}

// AcceptOrder implements ports.KitchenCommandPort.
func (k *KitchenService) AcceptOrder(ctx context.Context, orderID int64) error {
	err := k.setOrderStatus(ctx, orderID, order.StatusAccepted)
	if err != nil {
		return err
	}
	return nil
}

// Cancel implements ports.KitchenCommandPort.
func (k *KitchenService) Cancel(ctx context.Context, orderID int64) error {
	err := k.setOrderStatus(ctx, orderID, order.StatusCanceled)
	if err != nil {
		return err
	}
	return nil
}

// MarkPreparing implements ports.KitchenCommandPort.
func (k *KitchenService) MarkPreparing(ctx context.Context, orderID int64) error {
	err := k.setOrderStatus(ctx, orderID, order.StatusPreparing)
	if err != nil {
		return err
	}
	return nil
}

// MarkReady implements ports.KitchenCommandPort.
func (k *KitchenService) MarkReady(ctx context.Context, orderID int64) error {
	err := k.setOrderStatus(ctx, orderID, order.StatusReady)
	if err != nil {
		return err
	}
	return nil
}

// Serve implements ports.KitchenCommandPort.
func (k *KitchenService) Serve(ctx context.Context, orderID int64) error {
	err := k.setOrderStatus(ctx, orderID, order.StatusServed)
	if err != nil {
		return err
	}
	return nil
}

var _ ports.KitchenCommandPort = (*KitchenService)(nil)

func (k *KitchenService) setOrderStatus(ctx context.Context, orderID int64, status order.OrderStatus) error {
	ord, err := k.orders.GetByID(ctx, orderID)
	if err != nil {
		return err
	}
	if ord == nil {
		return errors.New("order not found")
	}

	if order.OrderStatus.CanTransitTo(ord.Status, status) {
		ord.Status = status
	} else {
		return errors.New("invalid order status transition")
	}

	if err := k.orders.Save(ctx, ord); err != nil {
		return err
	}
	return nil
}
