import React, { useEffect, useState } from 'react'
import QRCode from 'qrcode'
import './TableOverview.css'

interface Table {
  id: string
  name: string
  qrCodeUrl: string
}

const TableOverview: React.FC = () => {
  const [tables, setTables] = useState<Table[]>([])

  useEffect(() => {
    const generateQRCodes = async () => {
      const tableIds = ['T001', 'T002', 'T003', 'T004', 'T005', 'T006']
      const baseUrl = window.location.origin
      
      const tablesWithQR = await Promise.all(
        tableIds.map(async (tableId) => {
          const tableUrl = `${baseUrl}/table/${tableId}`
          const qrCodeUrl = await QRCode.toDataURL(tableUrl, {
            width: 200,
            margin: 2,
            color: {
              dark: '#000000',
              light: '#FFFFFF'
            }
          })
          
          return {
            id: tableId,
            name: `โต๊ะ ${tableId}`,
            qrCodeUrl
          }
        })
      )
      
      setTables(tablesWithQR)
    }

    generateQRCodes()
  }, [])

  return (
    <div className="table-overview">
      <div className="header">
        <h1>ระบบสั่งอาหารผ่าน QR Code</h1>
        <p>สแกน QR Code เพื่อสั่งอาหารจากโต๊ะของคุณ</p>
      </div>
      
      <div className="tables-grid">
        {tables.map((table) => (
          <div key={table.id} className="table-card">
            <h3>{table.name}</h3>
            <div className="qr-code-container">
              <img src={table.qrCodeUrl} alt={`QR Code สำหรับ ${table.name}`} />
            </div>
            <p className="table-id">{table.id}</p>
            <a 
              href={`/table/${table.id}`} 
              className="test-link"
              target="_blank"
              rel="noopener noreferrer"
            >
              ทดสอบลิงก์
            </a>
          </div>
        ))}
      </div>
      
      <div className="admin-section">
        <a href="/admin" className="admin-link">
          เข้าสู่หน้าหลังร้าน
        </a>
      </div>
    </div>
  )
}

export default TableOverview
