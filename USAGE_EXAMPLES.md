# QR Order System - Usage Examples

## การใช้งานระบบ QR Order แบบครบครัน

### 🚀 การเริ่มต้นใช้งาน

#### 1. เริ่มต้น Backend Server
```bash
cd backend
go run main.go
```
Server จะรันที่ `http://localhost:5050`

#### 2. เริ่มต้น Frontend
```bash
cd frontend-vite
npm install
npm run dev
```
Frontend จะรันที่ `http://localhost:3000`

### 📱 การใช้งานสำหรับลูกค้า

#### เข้าสู่ระบบผ่าน QR Code
1. เปิด `http://localhost:3000`
2. เลือกโต๊ะ (เช่น T001, T002)
3. ระบบจะพาไปหน้าสั่งอาหาร

#### การสั่งอาหาร
1. ดูเมนูที่โหลดจาก backend API
2. คลิก "เพิ่ม" เพื่อใส่สินค้าในตระกร้า
3. คลิก "ตระกร้า" เพื่อดูรายการสั่ง
4. ปรับจำนวนสินค้าได้
5. คลิก "สั่งอาหาร" เพื่อส่งออเดอร์

### 👨‍💼 การใช้งานสำหรับ Admin

#### เข้าสู่หน้า Admin
- เปิด `http://localhost:3000/admin`

#### จัดการออเดอร์
- ดูออเดอร์ที่เข้ามา
- เปลี่ยนสถานะออเดอร์ (รอดำเนินการ → กำลังเตรียม → พร้อมเสิร์ฟ → เสร็จสิ้น)

#### จัดการเมนู
1. คลิก "📋 จัดการเมนู" ในหน้า admin
2. หรือเปิด `http://localhost:3000/admin/menu`

### 🍽️ การจัดการเมนูอาหาร

#### เพิ่มเมนูใหม่
1. คลิก "เพิ่มเมนูใหม่"
2. กรอกข้อมูล:
   - ชื่อเมนู (จำเป็น)
   - รายละเอียด
   - ราคา (บาท)
   - URL รูปภาพ (ถ้ามี)
3. อัพโหลดรูปภาพ (ถ้าต้องการ)
4. คลิก "เพิ่มเมนู"

#### แก้ไขเมนู
1. คลิก "แก้ไข" ที่เมนูที่ต้องการ
2. แก้ไขข้อมูลในฟอร์ม
3. เปลี่ยนสถานะ "พร้อมขาย" ได้
4. คลิก "บันทึกการแก้ไข"

#### อัพโหลดรูปภาพ
1. ในฟอร์มเพิ่ม/แก้ไขเมนู
2. เลือกไฟล์รูปภาพ (ไม่เกิน 10MB)
3. รองรับไฟล์ image ทุกประเภท
4. รูปจะถูกอัพโหลดหลังบันทึกเมนู

#### เปิด/ปิดการขาย
- คลิก "ปิดขาย" เพื่อหยุดขายเมนูนั้น
- คลิก "เปิดขาย" เพื่อเริ่มขายอีกครั้ง

#### ลบเมนู
- คลิก "ลบ" และยืนยันการลบ

### 🔧 API Testing Examples

#### ทดสอบ API ด้วย curl

##### ดูเมนูที่พร้อมขาย
```bash
curl http://localhost:5050/menu
```

##### เพิ่มสินค้าในตระกร้า
```bash
curl -X POST http://localhost:5050/cart/items \
  -H "Content-Type: application/json" \
  -d '{
    "table_code": "T001",
    "menu_item_id": 101,
    "qty": 2
  }'
```

##### ส่งออเดอร์
```bash
curl -X POST http://localhost:5050/orders/submit \
  -H "Content-Type: application/json" \
  -d '{
    "table_code": "T001"
  }'
```

##### Admin: ดูเมนูทั้งหมด
```bash
curl http://localhost:5050/admin/menu/
```

##### Admin: เพิ่มเมนูใหม่
```bash
curl -X POST http://localhost:5050/admin/menu/ \
  -H "Content-Type: application/json" \
  -d '{
    "name": "ต้มยำกุ้ง",
    "description": "ต้มยำกุ้งน้ำใส รสเปรี้ยวเผ็ด",
    "price": 12000,
    "image_url": ""
  }'
```

##### Admin: อัพโหลดรูปภาพ
```bash
curl -X POST http://localhost:5050/admin/menu/1/upload-image \
  -F "image=@/path/to/image.jpg"
```

### 📊 ตัวอย่างข้อมูลที่ระบบใช้

#### Menu Item Response
```json
{
  "id": 101,
  "name": "ข้าวผัดกุ้ง",
  "description": "ข้าวผัดกุ้งสด หอมกรุ่น",
  "price": 6500,
  "image_url": "/uploads/menu/fried-rice.jpg",
  "is_available": true
}
```

#### Order Response
```json
{
  "id": 1,
  "table_id": 1,
  "status": "SUBMITTED",
  "total_satang": 13000,
  "items": [
    {
      "menu_item_id": 101,
      "name": "ข้าวผัดกุ้ง",
      "unit_price_satang": 6500,
      "qty": 2
    }
  ]
}
```

### 🎯 Workflow ตัวอย่าง

#### Scenario 1: ลูกค้าสั่งอาหาร
1. ลูกค้าสแกน QR Code → เข้าหน้า `/table/T001`
2. ระบบโหลดเมนูจาก API
3. ลูกค้าเลือกอาหาร → เรียก `POST /cart/items`
4. ลูกค้าสั่งอาหาร → เรียก `POST /orders/submit`
5. ระบบแสดงหมายเลขออเดอร์

#### Scenario 2: Admin จัดการเมนู
1. Admin เข้าหน้า `/admin/menu`
2. ระบบโหลดเมนูทั้งหมดจาก `GET /admin/menu/`
3. Admin เพิ่มเมนูใหม่ → เรียก `POST /admin/menu/`
4. Admin อัพโหลดรูป → เรียก `POST /admin/menu/{id}/upload-image`
5. ระบบอัพเดตรายการเมนู

#### Scenario 3: Admin จัดการออเดอร์
1. Admin เข้าหน้า `/admin`
2. ระบบเชื่อมต่อ WebSocket สำหรับ real-time updates
3. มีออเดอร์ใหม่เข้ามา → แสดงใน dashboard
4. Admin เปลี่ยนสถานะ → เรียก `POST /admin/orders/{id}/accept`

### 🔍 การ Debug และ Troubleshooting

#### ตรวจสอบ Backend
```bash
# ดู logs
go run main.go

# ทดสอบ API
curl http://localhost:5050/menu
```

#### ตรวจสอบ Frontend
1. เปิด Browser Developer Tools
2. ดู Network tab สำหรับ API calls
3. ดู Console สำหรับ JavaScript errors

#### ปัญหาที่พบบ่อย

1. **CORS Error**
   - ตรวจสอบ CORS settings ใน backend
   - ตรวจสอบ URL ใน frontend

2. **Image ไม่แสดง**
   - ตรวจสอบ static file serving
   - ตรวจสอบ image URLs

3. **API Connection Failed**
   - ตรวจสอบ backend server status
   - ตรวจสอบ port และ URL

### 📈 Performance Tips

#### Backend
- ใช้ database connection pooling
- เพิ่ม caching สำหรับ menu items
- Optimize image serving

#### Frontend
- ใช้ image lazy loading
- เพิ่ม loading states
- Implement error boundaries

### 🔒 Security Considerations

#### Backend
- Input validation
- Rate limiting
- File upload security

#### Frontend
- XSS protection
- Input sanitization
- Secure API calls

### 🚀 Production Deployment

#### Backend
```bash
go build -o qr-order-server main.go
./qr-order-server
```

#### Frontend
```bash
npm run build
# Deploy dist/ folder to web server
```

#### Environment Variables
```bash
# Backend
DATABASE_URL=postgresql://...
PORT=5050

# Frontend
VITE_API_BASE_URL=https://api.yourserver.com
```
