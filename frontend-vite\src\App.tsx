import { BrowserRouter as Router, Routes, Route } from 'react-router-dom'
import TableOverview from './components/TableOverview'
import OrderPage from './components/OrderPage'
import AdminPage from './components/AdminPage'
import AdminMenuPage from './components/AdminMenuPage'
import './App.css'

function App() {
  return (
    <Router>
      <div className="app">
        <Routes>
          <Route path="/" element={<TableOverview />} />
          <Route path="/table/:tableId" element={<OrderPage />} />
          <Route path="/admin" element={<AdminPage />} />
          <Route path="/admin/menu" element={<AdminMenuPage />} />
        </Routes>
      </div>
    </Router>
  )
}

export default App
