// internal/adapters/pg/models.go
package pg

import "time"

type TableModel struct {
	ID        int64  `gorm:"primaryKey"`
	Code      string `gorm:"uniqueIndex"`
	Status    string `gorm:"type:text;not null;default:'AVAILABLE'"`
	CreatedAt time.Time
	UpdatedAt time.Time
}

func (TableModel) TableName() string { return "tables" }

type MenuItemModel struct {
	ID          int64 `gorm:"primaryKey"`
	Name        string
	Description string
	Price       int64
	ImageURL    string
	IsAvailable bool
	CreatedAt   time.Time
	UpdatedAt   time.Time
}

func (MenuItemModel) TableName() string { return "menu_items" }

type OrderModel struct {
	ID        int64            `gorm:"primaryKey"`
	TableID   int64            `gorm:"index;not null"`
	Status    string           `gorm:"type:text;not null;default:'DRAFT'"`
	Total     int64            `gorm:"not null;default:0"`
	Items     []OrderItemModel `gorm:"foreignKey:OrderID"`
	CreatedAt time.Time
	UpdatedAt time.Time
}

func (OrderModel) TableName() string { return "orders" }

type OrderItemModel struct {
	ID         int64 `gorm:"primaryKey"`
	OrderID    int64 `gorm:"index;not null"`
	MenuItemID int64
	Name       string
	UnitPrice  int64
	Qty        int
	CreatedAt  time.Time
	UpdatedAt  time.Time
}

func (OrderItemModel) TableName() string { return "order_items" }
