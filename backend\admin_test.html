<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>QR Order - Admin Menu Management</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background-color: #f5f5f5;
        }
        
        .container {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        h1, h2 {
            color: #333;
        }
        
        .form-group {
            margin-bottom: 15px;
        }
        
        label {
            display: block;
            margin-bottom: 5px;
            font-weight: bold;
        }
        
        input, textarea, select {
            width: 100%;
            padding: 8px;
            border: 1px solid #ddd;
            border-radius: 4px;
            box-sizing: border-box;
        }
        
        button {
            background-color: #007bff;
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 4px;
            cursor: pointer;
            margin-right: 10px;
        }
        
        button:hover {
            background-color: #0056b3;
        }
        
        button.danger {
            background-color: #dc3545;
        }
        
        button.danger:hover {
            background-color: #c82333;
        }
        
        .menu-item {
            border: 1px solid #ddd;
            padding: 15px;
            margin-bottom: 10px;
            border-radius: 4px;
            background: #f9f9f9;
        }
        
        .menu-item img {
            max-width: 100px;
            max-height: 100px;
            object-fit: cover;
            border-radius: 4px;
        }
        
        .menu-item-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 10px;
        }
        
        .price {
            font-weight: bold;
            color: #28a745;
        }
        
        .status {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: bold;
        }
        
        .status.available {
            background-color: #d4edda;
            color: #155724;
        }
        
        .status.unavailable {
            background-color: #f8d7da;
            color: #721c24;
        }
        
        .error {
            color: #dc3545;
            background-color: #f8d7da;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .success {
            color: #155724;
            background-color: #d4edda;
            padding: 10px;
            border-radius: 4px;
            margin-bottom: 15px;
        }
        
        .grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }
        
        @media (max-width: 768px) {
            .grid {
                grid-template-columns: 1fr;
            }
        }
    </style>
</head>
<body>
    <h1>QR Order - Admin Menu Management</h1>
    
    <div class="grid">
        <!-- Create/Edit Menu Item Form -->
        <div class="container">
            <h2>Add/Edit Menu Item</h2>
            <div id="message"></div>
            
            <form id="menuForm">
                <input type="hidden" id="itemId" name="id">
                
                <div class="form-group">
                    <label for="name">Name:</label>
                    <input type="text" id="name" name="name" required>
                </div>
                
                <div class="form-group">
                    <label for="description">Description:</label>
                    <textarea id="description" name="description" rows="3"></textarea>
                </div>
                
                <div class="form-group">
                    <label for="price">Price (in satang, e.g., 6500 = 65.00 THB):</label>
                    <input type="number" id="price" name="price" required min="0">
                </div>
                
                <div class="form-group">
                    <label for="imageUrl">Image URL (optional):</label>
                    <input type="text" id="imageUrl" name="image_url">
                </div>
                
                <div class="form-group">
                    <label for="imageFile">Upload Image:</label>
                    <input type="file" id="imageFile" name="image" accept="image/*">
                </div>
                
                <div class="form-group">
                    <label for="isAvailable">Available:</label>
                    <select id="isAvailable" name="is_available">
                        <option value="true">Yes</option>
                        <option value="false">No</option>
                    </select>
                </div>
                
                <button type="submit" id="submitBtn">Add Menu Item</button>
                <button type="button" id="cancelBtn" onclick="resetForm()">Cancel</button>
            </form>
        </div>
        
        <!-- Menu Items List -->
        <div class="container">
            <h2>Menu Items</h2>
            <button onclick="loadMenuItems()">Refresh List</button>
            <button onclick="loadPublicMenu()">View Public Menu</button>
            
            <div id="menuList"></div>
        </div>
    </div>
    
    <script src="admin_api_examples.js"></script>
    <script>
        const API_BASE = 'http://localhost:5050';
        let editingItemId = null;
        
        // Load menu items on page load
        document.addEventListener('DOMContentLoaded', function() {
            loadMenuItems();
        });
        
        // Form submission handler
        document.getElementById('menuForm').addEventListener('submit', async function(e) {
            e.preventDefault();
            
            const formData = new FormData(e.target);
            const menuData = {
                name: formData.get('name'),
                description: formData.get('description'),
                price: parseInt(formData.get('price')),
                image_url: formData.get('image_url') || '',
                is_available: formData.get('is_available') === 'true'
            };
            
            try {
                let result;
                if (editingItemId) {
                    // Update existing item
                    result = await updateMenuItem(editingItemId, menuData);
                } else {
                    // Create new item
                    result = await createMenuItem(menuData);
                }
                
                // Upload image if provided
                const imageFile = formData.get('image');
                if (result && imageFile && imageFile.size > 0) {
                    const uploadResult = await uploadMenuImage(result.id, imageFile);
                    result = uploadResult.item;
                }
                
                showMessage('Menu item saved successfully!', 'success');
                resetForm();
                loadMenuItems();
                
            } catch (error) {
                showMessage('Error: ' + error.message, 'error');
            }
        });
        
        async function loadMenuItems() {
            try {
                const items = await getAllMenuItems();
                displayMenuItems(items);
            } catch (error) {
                showMessage('Error loading menu items: ' + error.message, 'error');
            }
        }
        
        async function loadPublicMenu() {
            try {
                const items = await getPublicMenu();
                displayMenuItems(items, true);
            } catch (error) {
                showMessage('Error loading public menu: ' + error.message, 'error');
            }
        }
        
        function displayMenuItems(items, isPublic = false) {
            const menuList = document.getElementById('menuList');
            
            if (!items || items.length === 0) {
                menuList.innerHTML = '<p>No menu items found.</p>';
                return;
            }
            
            menuList.innerHTML = items.map(item => `
                <div class="menu-item">
                    <div class="menu-item-header">
                        <h3>${item.name}</h3>
                        <div>
                            <span class="price">฿${(item.price / 100).toFixed(2)}</span>
                            ${!isPublic ? `<span class="status ${item.is_available ? 'available' : 'unavailable'}">
                                ${item.is_available ? 'Available' : 'Unavailable'}
                            </span>` : ''}
                        </div>
                    </div>
                    
                    ${item.image_url ? `<img src="${API_BASE}${item.image_url}" alt="${item.name}" onerror="this.style.display='none'">` : ''}
                    
                    <p>${item.description || 'No description'}</p>
                    
                    ${!isPublic ? `
                        <div>
                            <button onclick="editMenuItem(${item.id})">Edit</button>
                            <button onclick="toggleAvailability(${item.id}, ${item.is_available})" 
                                    class="${item.is_available ? 'danger' : ''}">
                                ${item.is_available ? 'Make Unavailable' : 'Make Available'}
                            </button>
                            <button onclick="deleteMenuItemConfirm(${item.id})" class="danger">Delete</button>
                        </div>
                    ` : ''}
                </div>
            `).join('');
        }
        
        async function editMenuItem(id) {
            try {
                const item = await getMenuItem(id);
                
                document.getElementById('itemId').value = item.id;
                document.getElementById('name').value = item.name;
                document.getElementById('description').value = item.description;
                document.getElementById('price').value = item.price;
                document.getElementById('imageUrl').value = item.image_url;
                document.getElementById('isAvailable').value = item.is_available.toString();
                
                document.getElementById('submitBtn').textContent = 'Update Menu Item';
                editingItemId = id;
                
                showMessage('Editing menu item: ' + item.name, 'success');
                
            } catch (error) {
                showMessage('Error loading menu item: ' + error.message, 'error');
            }
        }
        
        async function toggleAvailability(id, currentStatus) {
            try {
                const item = await getMenuItem(id);
                const updatedData = {
                    name: item.name,
                    description: item.description,
                    price: item.price,
                    image_url: item.image_url,
                    is_available: !currentStatus
                };
                
                await updateMenuItem(id, updatedData);
                showMessage('Menu item availability updated!', 'success');
                loadMenuItems();
                
            } catch (error) {
                showMessage('Error updating availability: ' + error.message, 'error');
            }
        }
        
        async function deleteMenuItemConfirm(id) {
            if (confirm('Are you sure you want to delete this menu item?')) {
                try {
                    await deleteMenuItem(id);
                    showMessage('Menu item deleted successfully!', 'success');
                    loadMenuItems();
                } catch (error) {
                    showMessage('Error deleting menu item: ' + error.message, 'error');
                }
            }
        }
        
        function resetForm() {
            document.getElementById('menuForm').reset();
            document.getElementById('itemId').value = '';
            document.getElementById('submitBtn').textContent = 'Add Menu Item';
            editingItemId = null;
            showMessage('', '');
        }
        
        function showMessage(message, type) {
            const messageDiv = document.getElementById('message');
            messageDiv.textContent = message;
            messageDiv.className = type;
        }
        
        // Include the API functions from admin_api_examples.js
        // (Make sure the file is loaded via script tag)
    </script>
</body>
</html>
