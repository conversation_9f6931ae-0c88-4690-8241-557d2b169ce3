package pg

import (
	"context"

	"github.com/micksudev/go-qr-order/internal/ports"
	"gorm.io/gorm"
)

type reposFacade struct {
	tables ports.TableRepo
	menus  ports.MenuRepo
	orders ports.OrderRepo
}

func (r *reposFacade) Tables() ports.TableRepo { return r.tables }
func (r *reposFacade) Menus() ports.MenuRepo   { return r.menus }
func (r *reposFacade) Orders() ports.OrderRepo { return r.orders }

// --- UnitOfWork with GORM ---
type Uow struct {
	db *gorm.DB
}

func NewUow(pg *PG) *Uow {
	return &Uow{db: pg.DB}
}

func (u *Uow) Do(ctx context.Context, fn func(r ports.Repos) error) error {
	return u.db.WithContext(ctx).Transaction(func(tx *gorm.DB) error {
		r := &reposFacade{
			tables: NewTablesTX(tx),
			menus:  NewMenusTX(tx),
			orders: NewOrdersTX(tx),
		}
		return fn(r)
	})
}
