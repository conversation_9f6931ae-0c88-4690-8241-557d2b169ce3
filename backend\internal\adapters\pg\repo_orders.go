// internal/adapters/pg/repo_orders.go
package pg

import (
	"context"
	"errors"

	dom "github.com/micksudev/go-qr-order/internal/core/domain"
	"github.com/micksudev/go-qr-order/internal/ports"

	"gorm.io/gorm"
)

var _ ports.OrderRepo = (*Orders)(nil)

type Orders struct{ db *gorm.DB }

func NewOrders(pg *PG) *Orders        { return &Orders{db: pg.DB} }
func NewOrdersTX(tx *gorm.DB) *Orders { return &Orders{db: tx} }

func (r *Orders) GetDraftByTable(ctx context.Context, tableID int64) (*dom.Order, error) {
	var m OrderModel
	if err := r.db.WithContext(ctx).
		Preload("Items").
		Where("table_id=? AND status='DRAFT'", tableID).
		First(&m).Error; err != nil {
		return nil, nil // ไม่มี draft → คืน nil
	}
	return toDomain(&m), nil
}

func (r *Orders) CreateDraft(ctx context.Context, tableID int64) (*dom.Order, error) {
	m := OrderModel{TableID: tableID, Status: "DRAFT", Total: 0}
	if err := r.db.WithContext(ctx).Create(&m).Error; err != nil {
		return nil, err
	}
	return &dom.Order{ID: m.ID, TableID: tableID, Status: dom.StatusDraft, Items: nil, Total: 0}, nil
}

func (r *Orders) Save(ctx context.Context, o *dom.Order) error {
	// upsert order + replace items
	var m OrderModel
	if err := r.db.WithContext(ctx).First(&m, o.ID).Error; err != nil {
		return errors.New("order not found")
	}
	m.Status = string(o.Status)
	m.Total = o.Total
	if err := r.db.WithContext(ctx).Save(&m).Error; err != nil {
		return err
	}
	// อัปเดต items (วิธีง่าย: ลบของเดิมแล้วใส่ใหม่)
	if err := r.db.WithContext(ctx).Where("order_id=?", m.ID).Delete(&OrderItemModel{}).Error; err != nil {
		return err
	}
	items := make([]OrderItemModel, 0, len(o.Items))
	for _, it := range o.Items {
		items = append(items, OrderItemModel{
			OrderID: m.ID, MenuItemID: it.MenuItemID, Name: it.Name, UnitPrice: it.UnitPrice, Qty: it.Qty,
		})
	}
	if len(items) > 0 {
		if err := r.db.WithContext(ctx).Create(&items).Error; err != nil {
			return err
		}
	}
	return nil
}

func (r *Orders) GetByID(ctx context.Context, id int64) (*dom.Order, error) {
	var m OrderModel
	if err := r.db.WithContext(ctx).Preload("Items").First(&m, id).Error; err != nil {
		return nil, errors.New("order not found")
	}
	return toDomain(&m), nil
}

func (r *Orders) ListAll(ctx context.Context) ([]dom.Order, error) {
	var models []OrderModel
	if err := r.db.WithContext(ctx).Preload("Items").Find(&models).Error; err != nil {
		return nil, err
	}

	orders := make([]dom.Order, len(models))
	for i, m := range models {
		orders[i] = *toDomain(&m)
	}

	return orders, nil
}

func (r *Orders) ListByStatus(ctx context.Context, status dom.OrderStatus) ([]dom.Order, error) {
	var models []OrderModel
	if err := r.db.WithContext(ctx).Preload("Items").Where("status = ?", string(status)).Find(&models).Error; err != nil {
		return nil, err
	}

	orders := make([]dom.Order, len(models))
	for i, m := range models {
		orders[i] = *toDomain(&m)
	}

	return orders, nil
}

func toDomain(m *OrderModel) *dom.Order {
	o := &dom.Order{ID: m.ID, TableID: m.TableID, Status: dom.OrderStatus(m.Status), Total: m.Total}
	o.Items = make([]dom.OrderItem, len(m.Items))
	for i, it := range m.Items {
		o.Items[i] = dom.OrderItem{
			MenuItemID: it.MenuItemID, Name: it.Name, UnitPrice: it.UnitPrice, Qty: it.Qty,
		}
	}
	return o
}
