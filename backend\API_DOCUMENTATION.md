# QR Order API Documentation

## Admin Menu Management APIs

### Base URL
```
http://localhost:5050/admin/menu
```

### 1. List All Menu Items
**GET** `/admin/menu/`

**Response:**
```json
[
  {
    "id": 1,
    "name": "Fried Rice",
    "description": "Delicious fried rice with vegetables",
    "price": 6500,
    "image_url": "/uploads/menu/fried-rice.jpg",
    "is_available": true
  }
]
```

### 2. Get Single Menu Item
**GET** `/admin/menu/{id}`

**Response:**
```json
{
  "id": 1,
  "name": "Fried Rice",
  "description": "Delicious fried rice with vegetables",
  "price": 6500,
  "image_url": "/uploads/menu/fried-rice.jpg",
  "is_available": true
}
```

### 3. Create Menu Item
**POST** `/admin/menu/`

**Request Body:**
```json
{
  "name": "Pad Thai",
  "description": "Traditional Thai stir-fried noodles",
  "price": 4500,
  "image_url": "/uploads/menu/pad-thai.jpg"
}
```

**Response:**
```json
{
  "id": 2,
  "name": "Pad Thai",
  "description": "Traditional Thai stir-fried noodles",
  "price": 4500,
  "image_url": "/uploads/menu/pad-thai.jpg",
  "is_available": true
}
```

### 4. Update Menu Item
**PUT** `/admin/menu/{id}`

**Request Body:**
```json
{
  "name": "Pad Thai Special",
  "description": "Premium Thai stir-fried noodles with shrimp",
  "price": 5500,
  "image_url": "/uploads/menu/pad-thai-special.jpg",
  "is_available": true
}
```

**Response:**
```json
{
  "id": 2,
  "name": "Pad Thai Special",
  "description": "Premium Thai stir-fried noodles with shrimp",
  "price": 5500,
  "image_url": "/uploads/menu/pad-thai-special.jpg",
  "is_available": true
}
```

### 5. Delete Menu Item
**DELETE** `/admin/menu/{id}`

**Response:**
```
204 No Content
```

### 6. Upload Menu Item Image
**POST** `/admin/menu/{id}/upload-image`

**Request:**
- Content-Type: `multipart/form-data`
- Form field: `image` (file)

**Response:**
```json
{
  "message": "Image uploaded successfully",
  "image_url": "/uploads/menu/2_1640995200.jpg",
  "item": {
    "id": 2,
    "name": "Pad Thai Special",
    "description": "Premium Thai stir-fried noodles with shrimp",
    "price": 5500,
    "image_url": "/uploads/menu/2_1640995200.jpg",
    "is_available": true
  }
}
```

## Public Menu API

### Get Available Menu Items
**GET** `/menu`

**Response:**
```json
[
  {
    "id": 1,
    "name": "Fried Rice",
    "description": "Delicious fried rice with vegetables",
    "price": 6500,
    "image_url": "/uploads/menu/fried-rice.jpg"
  }
]
```

## Static Files

### Uploaded Images
**GET** `/uploads/menu/{filename}`

Serves uploaded menu item images.

## Error Responses

All endpoints may return error responses in the following format:

**400 Bad Request:**
```
Invalid request data
```

**404 Not Found:**
```
Menu item not found
```

**500 Internal Server Error:**
```
Internal server error message
```

## Notes

1. **Price Format**: Prices are stored in satang (1/100 of Thai Baht). For example, 6500 = 65.00 THB
2. **Image Upload**: 
   - Maximum file size: 10 MB
   - Supported formats: All image types (image/*)
   - Files are stored in `uploads/menu/` directory
   - Filename format: `{menu_item_id}_{timestamp}.{extension}`
3. **Image URLs**: All image URLs are relative paths starting with `/uploads/`
4. **CORS**: The server allows requests from `http://localhost:3000` for frontend development

## Example Usage with curl

### Create a menu item:
```bash
curl -X POST http://localhost:5050/admin/menu/ \
  -H "Content-Type: application/json" \
  -d '{
    "name": "Tom Yum Soup",
    "description": "Spicy and sour Thai soup",
    "price": 8000,
    "image_url": ""
  }'
```

### Upload an image:
```bash
curl -X POST http://localhost:5050/admin/menu/1/upload-image \
  -F "image=@/path/to/image.jpg"
```

### Get all menu items:
```bash
curl http://localhost:5050/menu
```
