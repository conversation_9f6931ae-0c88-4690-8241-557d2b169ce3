// cmd/migrate/main.go
package main

import (
	"log"
	"os"

	"github.com/joho/godotenv"
	"github.com/micksudev/go-qr-order/internal/adapters/pg"
)

func main() {
	err := godotenv.Load()
	if err != nil {
		log.Fatal("Error loading .env file")
	}

	dsn := os.Getenv("DATABASE_URL")
	db, err := pg.New(dsn)
	if err != nil {
		log.Fatal(err)
	}
	if err := db.DB.AutoMigrate(
		&pg.TableModel{},
		&pg.MenuItemModel{},
		&pg.OrderModel{},
		&pg.OrderItemModel{},
	); err != nil {
		log.Fatal(err)
	}
	log.Println("migrated")
}
