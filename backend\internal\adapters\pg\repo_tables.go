// internal/adapters/pg/repo_tables.go
package pg

import (
	"context"
	"errors"

	dom "github.com/micksudev/go-qr-order/internal/core/domain"
	"github.com/micksudev/go-qr-order/internal/ports"
	"gorm.io/gorm"
)

var _ ports.TableRepo = (*Tables)(nil)

type Tables struct{ db *gorm.DB }

// UpdateStatus implements ports.TableRepo.
func (r *Tables) UpdateStatus(ctx context.Context, tableID int64, status dom.TableStatus) error {
	return r.db.WithContext(ctx).Model(&TableModel{}).Where("id = ?", tableID).Update("status", string(status)).Error
}

func NewTables(pg *PG) *Tables        { return &Tables{db: pg.DB} }
func NewTablesTX(tx *gorm.DB) *Tables { return &Tables{db: tx} }

func (r *Tables) FindByCode(ctx context.Context, code string) (*dom.Table, error) {
	var m TableModel
	if err := r.db.WithContext(ctx).Where("code = ?", code).First(&m).Error; err != nil {
		return nil, errors.New("table not found")
	}
	return &dom.Table{ID: m.ID, Code: m.Code, Status: dom.TableStatus(m.Status)}, nil
}

func (r *Tables) ListAll(ctx context.Context) ([]dom.Table, error) {
	var models []TableModel
	if err := r.db.WithContext(ctx).Find(&models).Error; err != nil {
		return nil, err
	}

	tables := make([]dom.Table, len(models))
	for i, m := range models {
		tables[i] = dom.Table{
			ID:     m.ID,
			Code:   m.Code,
			Status: dom.TableStatus(m.Status),
		}
	}

	return tables, nil
}
