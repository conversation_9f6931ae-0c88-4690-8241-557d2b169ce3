# QR Order Frontend Guide

## Overview
Frontend React application ที่เชื่อมต่อกับ Go backend API สำหรับระบบสั่งอาหารผ่าน QR Code

## Features ที่อัพเดตแล้ว

### 🍽️ Customer Features
- **Dynamic Menu Loading**: โหลดเมนูจาก backend API แบบ real-time
- **Cart Management**: เพิ่ม/ลบสินค้าในตระกร้าผ่าน API
- **Order Submission**: ส่งออเดอร์ไปยัง backend
- **Image Support**: แสดงรูปภาพเมนูจาก backend
- **Error Handling**: จัดการ error และ loading states

### 👨‍💼 Admin Features
- **Menu Management**: จัดการเมนูอาหารแบบครบครัน
  - เพิ่มเมนูใหม่
  - แก้ไขเมนูที่มีอยู่
  - ลบเมนู
  - เปิด/ปิดการขาย
  - อัพโหลดรูปภาพ
- **Order Management**: จัดการออเดอร์ (เตรียมไว้สำหรับ WebSocket)
- **Real-time Updates**: รองรับ WebSocket สำหรับอัพเดต real-time

## API Integration

### Services Structure
```
src/services/
├── api.ts          # API service functions
```

### API Endpoints ที่ใช้

#### Public APIs (Customer)
- `GET /menu` - ดูเมนูที่พร้อมขาย
- `POST /cart/items` - เพิ่มสินค้าในตระกร้า
- `POST /orders/submit` - ส่งออเดอร์

#### Admin APIs
- `GET /admin/menu/` - ดูเมนูทั้งหมด
- `POST /admin/menu/` - เพิ่มเมนูใหม่
- `PUT /admin/menu/{id}` - แก้ไขเมนู
- `DELETE /admin/menu/{id}` - ลบเมนู
- `POST /admin/menu/{id}/upload-image` - อัพโหลดรูปภาพ

## Pages และ Routes

### Customer Routes
- `/` - หน้าแรก (Table Overview)
- `/table/{tableId}` - หน้าสั่งอาหาร

### Admin Routes
- `/admin` - หน้าจัดการออเดอร์
- `/admin/menu` - หน้าจัดการเมนู

## Components ที่อัพเดต

### OrderPage.tsx
- ใช้ `menuAPI.getAvailableItems()` แทนข้อมูล mock
- ใช้ `orderAPI.addItem()` และ `orderAPI.submitOrder()`
- เพิ่ม loading และ error states
- รองรับรูปภาพจาก backend

### AdminMenuPage.tsx (ใหม่)
- หน้าจัดการเมนูแบบครบครัน
- Form สำหรับเพิ่ม/แก้ไขเมนู
- อัพโหลดรูปภาพ
- จัดการสถานะการขาย

### AdminPage.tsx
- เพิ่มลิงก์ไปหน้าจัดการเมนู
- เตรียมไว้สำหรับ WebSocket integration

## การติดตั้งและรัน

### Prerequisites
- Node.js 18+
- Backend server รันที่ `http://localhost:5050`

### Installation
```bash
cd frontend-vite
npm install
```

### Development
```bash
npm run dev
```
Frontend จะรันที่ `http://localhost:3000`

### Build for Production
```bash
npm run build
```

## Configuration

### API Base URL
ตั้งค่าใน `src/services/api.ts`:
```typescript
const API_BASE = 'http://localhost:5050';
```

### CORS Settings
Backend ต้องอนุญาต requests จาก `http://localhost:3000`

## Error Handling

### API Errors
- แสดง error messages ที่เข้าใจง่าย
- Retry mechanisms สำหรับ network errors
- Loading states ระหว่างรอ API response

### Image Loading
- Fallback ไปยัง placeholder image หากโหลดรูปไม่ได้
- รองรับ image URLs จาก backend

## WebSocket Integration (เตรียมไว้)

### OrderWebSocket Class
```typescript
const ws = new OrderWebSocket(
  (data) => console.log('Received:', data),
  (connected) => console.log('Connected:', connected)
);
ws.connect();
```

## File Upload

### Image Upload
- รองรับไฟล์รูปภาพทุกประเภท
- จำกัดขนาดไฟล์ 10MB
- แสดง preview ก่อนอัพโหลด
- Error handling สำหรับไฟล์ที่ไม่ถูกต้อง

## Responsive Design
- รองรับ mobile และ tablet
- Grid layout ที่ปรับตัวได้
- Modal และ form ที่ responsive

## Best Practices

### State Management
- ใช้ React hooks สำหรับ local state
- Centralized API calls ใน services
- Error boundaries สำหรับ error handling

### Performance
- Lazy loading สำหรับรูปภาพ
- Debounced API calls
- Optimistic updates สำหรับ UX ที่ดี

### Security
- Input validation
- XSS protection
- CSRF protection (ถ้าจำเป็น)

## Troubleshooting

### Common Issues

1. **API Connection Failed**
   - ตรวจสอบว่า backend server รันอยู่
   - ตรวจสอบ CORS settings
   - ตรวจสอบ network connectivity

2. **Images Not Loading**
   - ตรวจสอบ image URLs
   - ตรวจสอบ static file serving ใน backend
   - ตรวจสอบ file permissions

3. **WebSocket Connection Issues**
   - ตรวจสอบ WebSocket endpoint
   - ตรวจสอบ firewall settings
   - ตรวจสอบ browser WebSocket support

### Debug Mode
เปิด browser developer tools เพื่อดู:
- Network requests
- Console errors
- WebSocket messages

## Future Enhancements

### Planned Features
- Real-time order updates via WebSocket
- Push notifications
- Offline support
- Advanced filtering และ search
- Multi-language support
- Payment integration

### Performance Improvements
- Code splitting
- Service worker
- Image optimization
- Caching strategies

## Contributing

### Code Style
- ใช้ TypeScript
- ESLint และ Prettier
- Component naming conventions
- File organization standards

### Testing
- Unit tests สำหรับ components
- Integration tests สำหรับ API calls
- E2E tests สำหรับ user flows

## Support

หากมีปัญหาหรือข้อสงสัย:
1. ตรวจสอบ console errors
2. ตรวจสอบ network tab
3. ตรวจสอบ backend logs
4. ดู API documentation
