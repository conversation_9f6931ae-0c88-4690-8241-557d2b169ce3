// Admin Menu API Examples
// Base URL for the API
const API_BASE = 'http://localhost:5050';

// Helper function to handle API responses
async function handleResponse(response) {
  if (!response.ok) {
    const error = await response.text();
    throw new Error(`HTTP ${response.status}: ${error}`);
  }
  
  if (response.status === 204) {
    return null; // No content
  }
  
  return await response.json();
}

// 1. Get all menu items (Admin)
async function getAllMenuItems() {
  try {
    const response = await fetch(`${API_BASE}/admin/menu/`);
    const items = await handleResponse(response);
    console.log('All menu items:', items);
    return items;
  } catch (error) {
    console.error('Error fetching menu items:', error);
  }
}

// 2. Get single menu item (Admin)
async function getMenuItem(id) {
  try {
    const response = await fetch(`${API_BASE}/admin/menu/${id}`);
    const item = await handleResponse(response);
    console.log('Menu item:', item);
    return item;
  } catch (error) {
    console.error('Error fetching menu item:', error);
  }
}

// 3. Create new menu item
async function createMenuItem(menuData) {
  try {
    const response = await fetch(`${API_BASE}/admin/menu/`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(menuData)
    });
    
    const item = await handleResponse(response);
    console.log('Created menu item:', item);
    return item;
  } catch (error) {
    console.error('Error creating menu item:', error);
  }
}

// 4. Update menu item
async function updateMenuItem(id, menuData) {
  try {
    const response = await fetch(`${API_BASE}/admin/menu/${id}`, {
      method: 'PUT',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify(menuData)
    });
    
    const item = await handleResponse(response);
    console.log('Updated menu item:', item);
    return item;
  } catch (error) {
    console.error('Error updating menu item:', error);
  }
}

// 5. Delete menu item
async function deleteMenuItem(id) {
  try {
    const response = await fetch(`${API_BASE}/admin/menu/${id}`, {
      method: 'DELETE'
    });
    
    await handleResponse(response);
    console.log('Menu item deleted successfully');
    return true;
  } catch (error) {
    console.error('Error deleting menu item:', error);
    return false;
  }
}

// 6. Upload menu item image
async function uploadMenuImage(id, imageFile) {
  try {
    const formData = new FormData();
    formData.append('image', imageFile);
    
    const response = await fetch(`${API_BASE}/admin/menu/${id}/upload-image`, {
      method: 'POST',
      body: formData
    });
    
    const result = await handleResponse(response);
    console.log('Image uploaded:', result);
    return result;
  } catch (error) {
    console.error('Error uploading image:', error);
  }
}

// 7. Get public menu (for customers)
async function getPublicMenu() {
  try {
    const response = await fetch(`${API_BASE}/menu`);
    const items = await handleResponse(response);
    console.log('Public menu:', items);
    return items;
  } catch (error) {
    console.error('Error fetching public menu:', error);
  }
}

// Example usage scenarios

// Example 1: Create a complete menu item with image
async function createMenuItemWithImage() {
  // First create the menu item
  const menuData = {
    name: "Green Curry",
    description: "Spicy Thai green curry with chicken",
    price: 9500, // 95.00 THB in satang
    image_url: "" // Will be updated after image upload
  };
  
  const newItem = await createMenuItem(menuData);
  
  if (newItem) {
    // Then upload an image (assuming you have a file input)
    const fileInput = document.getElementById('imageFile');
    if (fileInput && fileInput.files[0]) {
      const uploadResult = await uploadMenuImage(newItem.id, fileInput.files[0]);
      console.log('Complete menu item created with image:', uploadResult.item);
    }
  }
}

// Example 2: Update menu item availability
async function toggleMenuItemAvailability(id) {
  const item = await getMenuItem(id);
  if (item) {
    const updatedData = {
      name: item.name,
      description: item.description,
      price: item.price,
      image_url: item.image_url,
      is_available: !item.is_available // Toggle availability
    };
    
    await updateMenuItem(id, updatedData);
  }
}

// Example 3: Bulk operations
async function bulkUpdatePrices(priceIncrease) {
  const items = await getAllMenuItems();
  
  if (items) {
    for (const item of items) {
      const updatedData = {
        name: item.name,
        description: item.description,
        price: item.price + priceIncrease,
        image_url: item.image_url,
        is_available: item.is_available
      };
      
      await updateMenuItem(item.id, updatedData);
      console.log(`Updated price for ${item.name}`);
    }
  }
}

// Example 4: HTML form integration
function setupMenuForm() {
  const form = document.getElementById('menuForm');
  const imageInput = document.getElementById('imageInput');
  
  form.addEventListener('submit', async (e) => {
    e.preventDefault();
    
    const formData = new FormData(form);
    const menuData = {
      name: formData.get('name'),
      description: formData.get('description'),
      price: parseInt(formData.get('price')),
      image_url: ''
    };
    
    // Create menu item
    const newItem = await createMenuItem(menuData);
    
    // Upload image if provided
    if (newItem && imageInput.files[0]) {
      await uploadMenuImage(newItem.id, imageInput.files[0]);
    }
    
    // Refresh the menu list
    await getAllMenuItems();
  });
}

// Export functions for use in other modules
if (typeof module !== 'undefined' && module.exports) {
  module.exports = {
    getAllMenuItems,
    getMenuItem,
    createMenuItem,
    updateMenuItem,
    deleteMenuItem,
    uploadMenuImage,
    getPublicMenu,
    createMenuItemWithImage,
    toggleMenuItemAvailability,
    bulkUpdatePrices
  };
}
