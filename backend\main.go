// cmd/play/main.go
package main

import (
	"log"
	"net/http"
	"os"
	"time"

	"github.com/joho/godotenv"
	"github.com/micksudev/go-qr-order/internal/adapters/pg"

	chi "github.com/go-chi/chi/v5"
	httpadapter "github.com/micksudev/go-qr-order/internal/adapters/http"
	mem "github.com/micksudev/go-qr-order/internal/adapters/memory"
	ws "github.com/micksudev/go-qr-order/internal/adapters/ws"
	app "github.com/micksudev/go-qr-order/internal/app"
	dom "github.com/micksudev/go-qr-order/internal/core/domain"
)

func main() {
	// Load .env file if it exists (optional for Docker)
	err := godotenv.Load()
	if err != nil {
		log.Println("Warning: .env file not found, using environment variables")
	}

	// Wait for database to be ready (simple retry)
	dsn := os.Getenv("DATABASE_URL")
	var pgg *pg.PG
	for i := 0; i < 30; i++ {
		pgg, err = pg.New(dsn)
		if err == nil {
			break
		}
		log.Printf("Database not ready, retrying in 2 seconds... (%d/30)", i+1)
		time.Sleep(2 * time.Second)
	}
	if err != nil {
		log.Fatal("Failed to connect to database after 30 attempts:", err)
	}

	if err := pgg.DB.AutoMigrate(
		&pg.TableModel{}, &pg.MenuItemModel{},
		&pg.OrderModel{}, &pg.OrderItemModel{},
	); err != nil {
		log.Fatal(err)
	}

	// seed (ชั่วคราว)
	pgg.DB.FirstOrCreate(&pg.TableModel{Code: "T001"}, pg.TableModel{Code: "T001", Status: string(dom.StatusAvailable)})
	pgg.DB.FirstOrCreate(&pg.TableModel{Code: "T002"}, pg.TableModel{Code: "T002", Status: string(dom.StatusAvailable)})
	pgg.DB.FirstOrCreate(&pg.TableModel{Code: "T003"}, pg.TableModel{Code: "T003", Status: string(dom.StatusAvailable)})
	pgg.DB.FirstOrCreate(&pg.TableModel{Code: "T004"}, pg.TableModel{Code: "T004", Status: string(dom.StatusAvailable)})
	pgg.DB.FirstOrCreate(&pg.TableModel{Code: "T005"}, pg.TableModel{Code: "T005", Status: string(dom.StatusAvailable)})
	pgg.DB.FirstOrCreate(&pg.TableModel{Code: "T006"}, pg.TableModel{Code: "T006", Status: string(dom.StatusAvailable)})

	pgg.DB.FirstOrCreate(&pg.MenuItemModel{ID: 101}, pg.MenuItemModel{ID: 101, Name: "Fried Rice", Price: 6500, ImageURL: "/uploads/menu/default-fried-rice.jpg", IsAvailable: true})
	pgg.DB.FirstOrCreate(&pg.MenuItemModel{ID: 102}, pg.MenuItemModel{ID: 102, Name: "Pad Thai", Price: 4500, ImageURL: "/uploads/menu/default-pad-thai.jpg", IsAvailable: true})

	tables := pg.NewTables(pgg)
	menus := pg.NewMenus(pgg)
	orders := pg.NewOrders(pgg)
	uow := pg.NewUow(pgg)
	memBus := mem.NewMemoryBus()
	hub := ws.NewHub()
	wsServer := ws.NewServer(hub)

	memBus.Subscribe("order.submitted", func(topic string, payload []byte) {
		hub.Broadcast(topic, payload)
	})

	memBus.Subscribe("order.status.changed", func(topic string, payload []byte) {
		hub.Broadcast(topic, payload)
	})

	// seed เริ่มต้น
	//tables.SetTable(&dom.Table{ID: 1, Code: "T001", Status: dom.StatusAvailable})
	//menus.SetMenu(&dom.MenuItem{ID: 101, Name: "Fried Rice", Price: 6500, IsAvailable: true})
	//menus.SetMenu(&dom.MenuItem{ID: 102, Name: "Pad Thai", Price: 4500, IsAvailable: true})

	// use case
	svc := app.NewOrderService(uow, tables, menus, orders, memBus)
	adminMenuSvc := app.NewAdminMenuService(uow, menus)
	adminOrderSvc := app.NewAdminOrderService(uow, orders, tables)

	//customerSvc := app.NewCustomerService(tables, menus, orders, nil)
	//kitchenSvc := app.NewKitchenService(orders)
	// http server
	r := chi.NewRouter()
	httpSrv := httpadapter.NewServer(svc, svc, adminMenuSvc, adminOrderSvc)
	r.Mount("/", httpSrv.Routes())
	r.Get("/ws", wsServer.HandleWS)
	http.ListenAndServe(":8080", r)

	//httpSrv := httpadapter.NewServer(svc, svc)
	//log.Println("HTTP :8080")
	log.Fatal(http.ListenAndServe(":8080", httpSrv.Routes()))
}
